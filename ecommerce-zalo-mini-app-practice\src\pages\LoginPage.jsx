import React, { useState, useEffect } from 'react';
import { Page, Box, Button, Input, useNavigate } from 'zmp-ui';
import { showToast } from 'zmp-sdk/apis';
import { useAtom } from 'jotai';
import {
  userAtom,
  isLoadingAtom,
  authActionsAtom,
  referralCodeAtom
} from '../store/authStore';
import { authActions } from '../store/authStore';
import ApiDebugger from '../components/ApiDebugger';

const LoginPage = () => {
  const navigate = useNavigate();
  const [user, setUser] = useAtom(userAtom);
  const [isLoading, setIsLoading] = useAtom(isLoadingAtom);
  const [, dispatch] = useAtom(authActionsAtom);
  const [referralCode] = useAtom(referralCodeAtom);

  const [formData, setFormData] = useState({
    phoneNumber: '',
    password: '',
    referralCodeInput: referralCode || '',
  });

  const [isRegisterMode, setIsRegisterMode] = useState(false);

  useEffect(() => {
    // If user is already authenticated, redirect to home
    if (user && user.accessToken) {
      navigate('/');
    }
  }, [user, navigate]);

  useEffect(() => {
    // Update referral code input when referralCode atom changes
    setFormData(prev => ({
      ...prev,
      referralCodeInput: referralCode || ''
    }));
  }, [referralCode]);

  const handleInputChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleZaloLogin = async () => {
    dispatch({ type: 'LOGIN_START' });

    try {
      const result = await authActions.loginWithZalo(formData.referralCodeInput);
      
      if (result.success) {
        dispatch({ 
          type: 'LOGIN_SUCCESS', 
          payload: { user: result.user } 
        });
        
        showToast({
          message: result.message,
          type: 'success',
          duration: 2000
        });

        // Navigate to home page
        navigate('/');
      } else {
        dispatch({ 
          type: 'LOGIN_ERROR', 
          payload: { error: result.error } 
        });
        
        showToast({
          message: result.error,
          type: 'error',
          duration: 3000
        });
      }
    } catch (error) {
      dispatch({ 
        type: 'LOGIN_ERROR', 
        payload: { error: 'Đăng nhập thất bại' } 
      });
      
      showToast({
        message: 'Đăng nhập thất bại',
        type: 'error',
        duration: 3000
      });
    }
  };

  const handlePhoneLogin = async () => {
    if (!formData.phoneNumber || !formData.password) {
      showToast({
        message: 'Vui lòng nhập đầy đủ thông tin',
        type: 'error',
        duration: 2000
      });
      return;
    }

    // Validate phone number format
    const phoneRegex = /^(0|\+84)[0-9]{9,10}$/;
    if (!phoneRegex.test(formData.phoneNumber)) {
      showToast({
        message: 'Số điện thoại không đúng định dạng',
        type: 'error',
        duration: 2000
      });
      return;
    }

    // Validate password length for register
    if (isRegisterMode && formData.password.length < 8) {
      showToast({
        message: 'Mật khẩu phải có ít nhất 8 ký tự',
        type: 'error',
        duration: 2000
      });
      return;
    }

    dispatch({ type: 'LOGIN_START' });

    try {
      const result = isRegisterMode 
        ? await authActions.register(formData.phoneNumber, formData.password, formData.referralCodeInput)
        : await authActions.loginWithPhone(formData.phoneNumber, formData.password);
      
      if (result.success) {
        dispatch({ 
          type: 'LOGIN_SUCCESS', 
          payload: { user: result.user } 
        });
        
        showToast({
          message: result.message,
          type: 'success',
          duration: 2000
        });

        // Navigate to home page
        navigate('/');
      } else {
        dispatch({ 
          type: 'LOGIN_ERROR', 
          payload: { error: result.error } 
        });
        
        showToast({
          message: result.error,
          type: 'error',
          duration: 3000
        });
      }
    } catch (error) {
      dispatch({ 
        type: 'LOGIN_ERROR', 
        payload: { error: isRegisterMode ? 'Đăng ký thất bại' : 'Đăng nhập thất bại' } 
      });
      
      showToast({
        message: isRegisterMode ? 'Đăng ký thất bại' : 'Đăng nhập thất bại',
        type: 'error',
        duration: 3000
      });
    }
  };

  const toggleMode = () => {
    setIsRegisterMode(!isRegisterMode);
  };

  return (
    <Page className="min-h-screen bg-white">
      <Box className="p-6">
        {/* API Debugger - Only show in development */}
        {process.env.REACT_APP_ENV === 'development' && <ApiDebugger />}

        {/* Header */}
        <Box className="text-center mb-8">
          <h1 className="text-2xl font-bold text-brand-blue mb-2">
            {isRegisterMode ? 'Đăng ký tài khoản' : 'Đăng nhập'}
          </h1>
          <p className="text-gray-600">
            {isRegisterMode
              ? 'Tạo tài khoản mới để bắt đầu mua sắm'
              : 'Chào mừng bạn quay trở lại!'
            }
          </p>
        </Box>

        {/* Zalo Login Button */}
        <Box className="mb-6">
          <Button
            fullWidth
            size="large"
            onClick={handleZaloLogin}
            disabled={isLoading}
            className="bg-blue-500 text-white py-3 rounded-lg font-medium"
          >
            {isLoading ? 'Đang xử lý...' : 'Đăng nhập với Zalo'}
          </Button>
        </Box>

        {/* Divider */}
        <Box className="flex items-center mb-6">
          <div className="flex-1 border-t border-gray-300"></div>
          <span className="px-4 text-gray-500 text-sm">hoặc</span>
          <div className="flex-1 border-t border-gray-300"></div>
        </Box>

        {/* Phone Login Form */}
        <Box className="space-y-4 mb-6">
          <Input
            placeholder="Số điện thoại"
            value={formData.phoneNumber}
            onChange={(e) => handleInputChange('phoneNumber', e.target.value)}
            type="tel"
            className="w-full p-3 border border-gray-300 rounded-lg"
          />
          
          <Input
            placeholder="Mật khẩu"
            value={formData.password}
            onChange={(e) => handleInputChange('password', e.target.value)}
            type="password"
            className="w-full p-3 border border-gray-300 rounded-lg"
          />

          {/* Referral Code Input */}
          <Input
            placeholder="Mã giới thiệu (tùy chọn)"
            value={formData.referralCodeInput}
            onChange={(e) => handleInputChange('referralCodeInput', e.target.value)}
            className="w-full p-3 border border-gray-300 rounded-lg"
          />
          
          <Button
            fullWidth
            size="large"
            onClick={handlePhoneLogin}
            disabled={isLoading}
            className="bg-brand-blue text-white py-3 rounded-lg font-medium"
          >
            {isLoading 
              ? 'Đang xử lý...' 
              : (isRegisterMode ? 'Đăng ký' : 'Đăng nhập')
            }
          </Button>
        </Box>

        {/* Toggle Mode */}
        <Box className="text-center">
          <button
            onClick={toggleMode}
            className="text-brand-blue font-medium underline"
          >
            {isRegisterMode 
              ? 'Đã có tài khoản? Đăng nhập ngay' 
              : 'Chưa có tài khoản? Đăng ký ngay'
            }
          </button>
        </Box>

        {/* Referral Code Info */}
        {formData.referralCodeInput && (
          <Box className="mt-6 p-4 bg-blue-50 rounded-lg">
            <p className="text-sm text-blue-700">
              <strong>Mã giới thiệu:</strong> {formData.referralCodeInput}
            </p>
            <p className="text-xs text-blue-600 mt-1">
              Bạn sẽ nhận được ưu đãi đặc biệt khi sử dụng mã này!
            </p>
          </Box>
        )}
      </Box>
    </Page>
  );
};

export default LoginPage;

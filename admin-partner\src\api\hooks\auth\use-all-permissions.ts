import { useState, useEffect } from "react";
import { useFunction } from "@/src/api/hooks/function/use-function";
import { useStoreId } from "@/src/hooks/use-store-id";
import { PermissionType } from "@/src/constants/constant";

export const useAllPermissions = () => {
  const { getAllPermissions } = useFunction();
  const shopId = useStoreId();
  const [permissions, setPermissions] = useState<Record<string, PermissionType[]>>({});
  const [loading, setLoading] = useState(true);
  const [isAgency, setIsAgency] = useState(false);

  useEffect(() => {
    const fetchPermissions = async () => {
      if (!shopId) {
        setPermissions({});
        setIsAgency(false);
        setLoading(false);
        return;
      }

      try {
        setLoading(true);
        const res = await getAllPermissions();
        if (res?.status === 200 && res.data.data) {
          const perms = res.data.data.permissions.reduce((acc, item) => {
            acc[item.url] = item.permissions;
            return acc;
          }, {} as Record<string, PermissionType[]>);
          setPermissions(perms);
          setIsAgency(res.data.data.isAgency);
        } else {
          // throw new Error("Phản hồi API không hợp lệ");
        }
      } catch (error) {
        console.error("Lỗi lấy tất cả quyền:", error);
        setPermissions({});
        setIsAgency(false);
      } finally {
        setLoading(false);
      }
    };

    fetchPermissions();
  }, [shopId]);

  return {
    permissions,
    loading,
    isAgency,
  };
};

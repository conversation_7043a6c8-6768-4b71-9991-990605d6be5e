import { Logger } from "nextjs-api-lib";
import { enqueueSnackbar } from "notistack";
import { logger } from "@/src/utils/logger";
import type { ErrorConfig, ApiError } from "../types/error.types";
import { paths } from "@/src/paths";
import Router from "next/router";
import { isDevelopment } from "@/src/utils/env-helper";

export class ErrorHandlerService {
  static handle(
    error: any,
    config: ErrorConfig = { showSnackbar: true, logError: true }
  ): ApiError {
    const status = error.response?.status || error.status || 500;

    const errorResponse = {
      status,
      title: error.title || error.response?.data?.title || "Error",
      detail:
        error.detail ||
        error.response?.data?.detail ||
        error.message ||
        error.response?.data?.message,
      type: error.type || error.response?.data?.type || error.name,
      errors: error.errors || error.response?.data?.errors || {},
      extensions: error.extensions || error.response?.data?.extensions || {},
      instance: error.instance || error.response?.data?.instance || null,
    };

    if (!isDevelopment) {
      this.handleErrorRedirect(status);
    }

    if (config.showSnackbar) {
      this.showErrorSnackbar(errorResponse);
    }

    if (config.logError) {
      logger.error("API Error:", {
        status: errorResponse.status,
        message: errorResponse.detail,
        type: errorResponse.type,
      });
    }

    return errorResponse;
  }

  private static handleErrorRedirect(status: number): void {
    const needsRedirect = [401, 403, 404, 500].includes(status);
    if (!needsRedirect) return;

    switch (status) {
      case 401:
        Router.push(paths.auth.login);
        break;
      case 403:
        Router.push(paths.forbidden);
        break;
      case 404:
        Router.push(paths.notFound);
        break;
      case 500:
        Router.push(paths.auth.login);
        break;
    }
  }

  private static showErrorSnackbar(error: ApiError): void {
    let message = error.detail || error.title || "An error occurred";

    // Customize message based on status code
    switch (error.status) {
      case 401:
        message = "Unauthorized access. Please login again.";
        break;
      case 403:
        message = "You do not have permission to perform this action.";
        break;
      case 404:
        message = "The requested resource was not found.";
        break;
      case 500:
        message = "An internal server error occurred. Please try again later.";
        break;
    }

    enqueueSnackbar(message, {
      variant: "error",
      anchorOrigin: {
        vertical: "top",
        horizontal: "right",
      },
      autoHideDuration: 3000,
    });
  }
}

// Request Types
export interface GetGroupFileRequest {
  partnerId: string;
  search?: string;
  skip: number;
  limit: number;
}

export interface GetFileGroupRequest {
  groupFileId: string;
  skip: number;
  limit: number;
  search?: string;
}

export interface CreateFileGroupRequest {
  groupFileId: string;
  file: File;
}

export interface CreateGroupRequest {
  groupName: string;
  desc?: string;
}

export interface UpdateGroupRequest {
  groupFileId: string;
  groupName: string;
  desc?: string;
}

// Response Types
export interface MediaGroup {
  groupFileId: string;
  groupName: string;
  desc?: string;
  numberFile: number;
  created: string;
  updated: string;
}

export interface MediaFile {
  mediaFileId: string;
  groupFileId: string;
  type: 'IMAGE' | 'VIDEO';
  link: string;
  created: string;
  updated: string;
}

export interface BaseResponse<T> {
  data: {
    data: T[];
    total: number;
    page: number;
    limit: number;
  };
  status: number;
  message: string;
}

export interface MediaResponse extends BaseResponse<MediaFile> {}
export interface MediaGroupResponse extends BaseResponse<MediaGroup> {} 
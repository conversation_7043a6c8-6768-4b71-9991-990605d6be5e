import React, { useEffect } from 'react';
import { useAtom } from 'jotai';
import { referralCodeAtom, authActions<PERSON>tom } from '../store/authStore';
import { showToast } from 'zmp-sdk/apis';

const ReferralHandler = () => {
  const [referralCode, setReferralCode] = useAtom(referralCodeAtom);
  const [, dispatch] = useAtom(authActionsAtom);

  useEffect(() => {
    // Check for referral code in URL parameters
    const urlParams = new URLSearchParams(window.location.search);
    const refFromUrl = urlParams.get('ref') || urlParams.get('referralCode') || urlParams.get('refCode');
    
    if (refFromUrl && refFromUrl !== referralCode) {
      // Store referral code
      dispatch({ 
        type: 'SET_REFERRAL_CODE', 
        payload: { code: refFromUrl } 
      });

      // Show notification to user
      showToast({
        message: `Đã áp dụng mã giới thiệu: ${refFromUrl}`,
        type: 'success',
        duration: 3000
      });

      // Clean URL (remove referral code from URL)
      const newUrl = new URL(window.location);
      newUrl.searchParams.delete('ref');
      newUrl.searchParams.delete('referralCode');
      newUrl.searchParams.delete('refCode');
      
      // Update URL without page reload
      window.history.replaceState({}, document.title, newUrl.toString());
    }
  }, [referralCode, dispatch]);

  // This component doesn't render anything
  return null;
};

export default ReferralHandler;

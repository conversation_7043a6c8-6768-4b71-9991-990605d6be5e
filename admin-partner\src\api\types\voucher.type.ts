export type TypeRelease = "Free" | "ExchangePoints";

export type TypeDiscount = "Percent" | "Money" | "None";

export type TypeLimit = "All" | "Category" | "Product" | "NotRequired" | "MinMoneyRequired";

export type TypeCondition = "All" | "Group" | "Customer";

export type TypeActive = "Actived" | "InActived";

export type SearchType = "All" | "Publish" | "Expired";

export type TypeVoucher = "Promotion" | "Transport" | "PointPromotion";

export enum VoucherType {
  Promotion = "Promotion",
  Transport = "Transport",
  PointPromotion = "PointPromotion",
}

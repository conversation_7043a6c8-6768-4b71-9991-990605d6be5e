import { apiClient } from "../../config/api-client";
import { API_PATHS } from "../../constants/api-paths";
import type { ApiError, ErrorConfig } from "../../types/error.types";
import type { ExtendedRequestConfig } from "../../types/api.types";
import { logger } from "@/src/utils/logger";
import { UpdateUserInfoBody } from "../../types/user.types";

function handleApiError(error: any): never {
  const status = error.response?.status || 500;
  const errorData = error.response?.data as ApiError;
  const errorMessage = errorData?.detail || errorData?.title || error.message;

  if (error.response) {
    throw {
      ...error,
      message: errorMessage,
    };
  }

  throw error;
}

export const userService = {
  createUser: async <T = any>(data: any, errorConfig?: ErrorConfig) => {
    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
    };

    try {
      const response = await apiClient.post<T>(API_PATHS.USER.CREATE, data, config);

      return response;
    } catch (error: any) {
      handleApiError(error);
    }
  },

  createUserAddress: async <T = any>(data: any, errorConfig?: ErrorConfig) => {
    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
    };

    try {
      const response = await apiClient.post<T>(API_PATHS.USER.CREATE_USER_ADDRESS, data, config);
      return response;
    } catch (error: any) {
      handleApiError(error);
    }
  },

  listTag: async <T = any>(queryData: any, bodyData: any, errorConfig?: ErrorConfig) => {
    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
    };

    try {
      const response = await apiClient.get<T>(
        `${API_PATHS.USER.LIST_TAG}${queryData} &search=${bodyData.search}&shopId=${bodyData.shopId}`,
        bodyData,
        config
      );
      return response;
    } catch (error: any) {
      handleApiError(error);
    }
  },

  listAddress: async <T = any>(queryData: any, bodyData: any, errorConfig?: ErrorConfig) => {
    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
    };

    try {
      const response = await apiClient.get<T>(
        `${API_PATHS.USER.LIST_ADDRESS}${queryData}&userId=${bodyData.userId}`,
        bodyData,
        config
      );
      return response;
    } catch (error: any) {
      handleApiError(error);
    }
  },

  updateUser: async <T = any>(data: any, errorConfig?: ErrorConfig) => {
    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
    };

    try {
      const response = await apiClient.put<T>(API_PATHS.USER.UPDATE_USER, data, config);
      return response;
    } catch (error: any) {
      handleApiError(error);
    }
  },

  updateUserAddress: async <T = any>(data: any, errorConfig?: ErrorConfig) => {
    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
    };

    try {
      const response = await apiClient.put<T>(API_PATHS.USER.UPDATE_USER_ADDRESS, data, config);
      return response;
    } catch (error: any) {
      handleApiError(error);
    }
  },

  detailUser: async <T = any>(userId: string, errorConfig?: ErrorConfig) => {
    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
    };

    try {
      const response = await apiClient.get<T>(`${API_PATHS.USER.DETAIL}?userId=${userId}`, config);

      return response;
    } catch (error: any) {
      handleApiError(error);
    }
  },

  listUser: async <T = any>(queryData: any, bodyData: any, errorConfig?: ErrorConfig) => {
    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
    };

    const queryParams = new URLSearchParams({
      shopId: bodyData.shopId,

      ...(bodyData.search && { search: bodyData.search }),
      ...(bodyData.affiliationStatus && { affiliationStatus: bodyData.affiliationStatus }),
    });

    try {
      const response = await apiClient.get<T>(
        `${API_PATHS.USER.LIST_USER}${queryData}&${queryParams.toString()}`,
        bodyData,
        config
      );
      return response;
    } catch (error: any) {
      handleApiError(error);
    }
  },
  deleteUserAddress: async <T = any>(addressId: string, errorConfig?: ErrorConfig) => {
    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
    };

    try {
      const response = await apiClient.delete<T>(
        `${API_PATHS.USER.DELETE_USER_ADDRESS}?shippingAddressId=${addressId}`,
        config
      );
      return response;
    } catch (error: any) {
      handleApiError(error);
    }
  },
  deleteUsers: async <T = any>(userIds: string[], errorConfig?: ErrorConfig) => {
    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
    };

    try {
      const response = await apiClient.delete<T>(`${API_PATHS.USER.DELETE_USERS}`, {
        ...config,
        data: userIds, // Truyền mảng trong body của request
      });
      return response;
    } catch (error: any) {
      handleApiError(error);
    }
  },
  listUserByUserIds: async <T = any>(
    data: { shopId: string; userIds: string[] },
    errorConfig?: ErrorConfig
  ) => {
    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
    };

    try {
      const response = await apiClient.post<T>(API_PATHS.USER.LIST_USER_BY_USER_IDS, data, config);
      return response;
    } catch (error: any) {
      handleApiError(error);
    }
  },
  updateUserInfo: async <T = any>(data: UpdateUserInfoBody, errorConfig?: ErrorConfig) => {
    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
    };

    const bodyData = {
      userId: data.userId,
      updateAction: data.updateAction,
      ...(data.notes && { notes: data.notes }), // Chỉ thêm notes nếu có giá trị
      ...(data.referrerCode && { referrerCode: data.referrerCode }),
      ...(data.tags && { tags: data.tags }),
      ...(data.password && { password: data.password }),
    };

    try {
      const response = await apiClient.put<any, T>(
        API_PATHS.USER.UPDATE_USER_INFO,
        bodyData,
        config
      );
      return response;
    } catch (error: any) {
      handleApiError(error);
    }
  },
  exportUserTemplate: async <T = any>(errorConfig?: ErrorConfig) => {
    const config: any = {
      errorHandling: errorConfig,
      responseType: "blob",
    };

    try {
      const response = await apiClient.get<T>(API_PATHS.USER.EXPORT_USER_TEMPLATE, config);
      return response;
    } catch (error: any) {
      handleApiError(error);
    }
  },
  importListUser: async <T = any>(file: File, shopId: string, errorConfig?: ErrorConfig) => {
    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
      headers: {
        "Content-Type": "multipart/form-data",
      },
    };
    const formData = new FormData();
    formData.append("file", file);
    formData.append("shopId", shopId);
    try {
      const response = await apiClient.post<T>(API_PATHS.USER.IMPORT_LIST_USER, formData, config);
      return response;
    } catch (error: any) {
      // handleApiError(error);
      return error;
    }
  },
  exportListUser: async <T = any>(
    params: {
      Search?: string;
      TagName?: string;
      ShopId?: string;
      AffiliationStatus?: string;
    },
    errorConfig?: ErrorConfig
  ) => {
    const config: any = {
      errorHandling: errorConfig,
      responseType: "blob",
    };

    const queryParams = new URLSearchParams();

    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== "") {
        queryParams.append(key, value.toString());
      }
    });

    try {
      const queryString = queryParams.toString();
      const url = queryString
        ? `${API_PATHS.USER.EXPORT_LIST_USER}?${queryString}`
        : API_PATHS.USER.EXPORT_LIST_USER;

      const response = await apiClient.get<T>(url, config);
      return response;
    } catch (error: any) {
      handleApiError(error);
    }
  },
};

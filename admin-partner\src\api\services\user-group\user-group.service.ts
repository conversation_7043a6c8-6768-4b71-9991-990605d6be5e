import { apiClient } from "../../config/api-client";
import { API_PATHS } from "../../constants/api-paths";
import type { ApiError, ErrorConfig } from "../../types/error.types";
import type { ExtendedRequestConfig } from "../../types/api.types";
import { logger } from "@/src/utils/logger";
import { UpdateUserInfoBody } from "../../types/user.types";

function handleApiError(error: any): never {
  const status = error.response?.status || 500;
  const errorData = error.response?.data as ApiError;
  const errorMessage = errorData?.detail || errorData?.title || error.message;

  if (error.response) {
    throw {
      ...error,
      message: errorMessage,
    };
  }

  throw error;
}
export interface PagingOptions {
  NameType?: string;
  SortType?: "asc" | "desc";
  PageSize?: number;
  PageIndex?: number;
  Search?: string;
  Name?: string;
  Sort?: "asc" | "desc";
}

export interface UserGroupQueryParams {
  ShopId: string;
  GroupName?: string;
  Paging?: PagingOptions;
}

export interface ParamImportExcelUserGroup {
  file: File | null;
  obj: {
    ShopId: string;
    GroupName?: string;
    Description?: string;
    GroupId?: string;
  };
}

export interface ConditionDto {
  field: string;
  operator: string;
  values: string[];
  valueType: string;
  logicOperator: string;
}

export interface FilterRequestDto {
  shopId: string;
  groupId?: string;
  conditions: ConditionDto[];
}

export interface UserDetailDto {
  groupId?: string;
  source?: string;
  userId?: string;
  referralCode?: string;
  fullname?: string;
  email?: string;
  phoneNumber?: string;
}

export interface UserGroupAdvancedSearchDto {
  shopId?: string;
  groupId?: string;
  groupName?: string;
  description?: string;
  isAuto?: boolean;
  status?: string;
  details?: UserDetailDto[];
  conditions?: ConditionDto[];
}

export interface PagingDto {
  pageSize: number;
  pageIndex: number;
  search: string;
  name?: string;
  sort?: string;
  nameType?: string;
  sortType?: string;
}

export interface SearchUserOfUserGroupDto {
  shopId: string;
  groupId: string;
  paging: PagingDto;
}

export interface BodyExportExcelListUser {
  shopId: string;
  groupId: string;
  paging: {
    pageSize: number;
    pageIndex: number;
    search: string;
    name: string;
    sort: string;
    nameType: string;
    sortType: string;
  };
}

export const userGroupService = {
  getListUserGroup: async <T = any>(params: UserGroupQueryParams, errorConfig?: ErrorConfig) => {
    const config: any = {
      errorHandling: errorConfig,
      params: {
        ShopId: params.ShopId,
        GroupName: params.GroupName,
        "Paging.NameType": "Created",
        "Paging.SortType": "asc",
        "Paging.PageSize": params.Paging?.PageSize,
        "Paging.PageIndex": params.Paging?.PageIndex,
        "Paging.Search": params.Paging?.Search,
        "Paging.Name": "Created",
        "Paging.Sort": "asc",
      },
    };

    try {
      if (params.ShopId) {
        const response = await apiClient.get<T>(
          `${API_PATHS.USER_GROUP.GET_LIST_USER_GROUP_BY_SHOP_ID}`,
          config
        );

        return response;
      }
    } catch (error: any) {
      handleApiError(error);
    }
  },
  deleteUserGroup: async <T = any>(shopId: string, groupId: string, errorConfig?: ErrorConfig) => {
    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
    };
    try {
      const response = await apiClient.delete<T>(
        `${API_PATHS.USER_GROUP.API_USER_GROUP}/${shopId}/${groupId}`,
        config
      );

      return response;
    } catch (error: any) {
      handleApiError(error);
    }
  },
  exportTemplateExcelUser: async <T = any>(errorConfig?: ErrorConfig) => {
    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
    };
    try {
      const response = await apiClient.get<T>(
        `${API_PATHS.USER_GROUP.EXPORT_TEMPLATE_EXCEL_USER}`,
        config
      );

      return response;
    } catch (error: any) {
      handleApiError(error);
    }
  },

  getListFilterUserGroup: async <T = any>(errorConfig?: ErrorConfig) => {
    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
    };
    try {
      const response = await apiClient.get<T>(
        `${API_PATHS.USER_GROUP.GET_LIST_FILTER_USER_GROUP}`,
        config
      );

      return response;
    } catch (error: any) {
      handleApiError(error);
    }
  },

  importExcelListUser: async <T = any>(
    data: ParamImportExcelUserGroup,
    errorConfig?: ErrorConfig
  ) => {
    const config: ExtendedRequestConfig = {
      headers: {
        "Content-Type": "multipart/form-data",
      },
      errorHandling: errorConfig,
    };

    try {
      if (data.obj.ShopId && data.file) {
        const formData = new FormData();
        formData.append("file", data.file);
        formData.append("ShopId", data.obj.ShopId);
        if (data.obj.GroupName) {
          formData.append("GroupName", data.obj.GroupName);
        }
        if (data.obj.GroupId) {
          formData.append("GroupId", data.obj.GroupId);
        }

        if (data.obj.Description) formData.append("Description", data.obj.Description);

        const response = await apiClient.post<T>(
          `${API_PATHS.USER_GROUP.IMPORT_EXCEL_LIST_USER}`,
          formData,
          config
        );
        return response;
      }
    } catch (error: any) {
      handleApiError(error);
    }
  },

  getListUserByGroupId: async <T = any>(
    data: SearchUserOfUserGroupDto,
    errorConfig?: ErrorConfig
  ) => {
    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
    };
    try {
      if (data.shopId && data.groupId) {
        data.paging.name = "Created";
        data.paging.sort = "asc";
        data.paging.nameType = "Created";
        data.paging.sortType = "asc";

        const response = await apiClient.post<T>(
          `${API_PATHS.USER_GROUP.API_USER_GROUP}/user`,
          data,
          config
        );
        return response;
      }
    } catch (error: any) {
      handleApiError(error);
    }
  },

  exportExcelListUserByGroupId: async <T = any>(
    data: BodyExportExcelListUser,
    errorConfig?: ErrorConfig
  ) => {
    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
    };
    try {
      if (data.shopId && data.groupId) {
        const response = await apiClient.post<T>(
          `${API_PATHS.USER_GROUP.EXPORT_LIST_USER}`,
          data,
          config
        );
        return response;
      }
    } catch (error: any) {
      handleApiError(error);
    }
  },

  filterListUserByAdvancedFilter: async <T = any>(
    data: FilterRequestDto,
    errorConfig?: ErrorConfig
  ) => {
    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
    };
    try {
      if (data.shopId) {
        const response = await apiClient.post<T>(
          `${API_PATHS.USER_GROUP.FILTER_LIST_USER}`,
          data,
          config
        );
        return response;
      }
    } catch (error: any) {
      handleApiError(error);
    }
  },
  createUserGroupWithAdvancedSearch: async <T = any>(
    data: UserGroupAdvancedSearchDto,
    errorConfig?: ErrorConfig
  ) => {
    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
    };
    try {
      if (data.shopId) {
        const response = await apiClient.post<T>(
          `${API_PATHS.USER_GROUP.API_USER_GROUP}`,
          data,
          config
        );
        return response;
      }
    } catch (error: any) {
      handleApiError(error);
    }
  },

  updateUserGroupWithAdvancedSearch: async <T = any>(
    data: UserGroupAdvancedSearchDto,
    errorConfig?: ErrorConfig
  ) => {
    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
    };
    try {
      if (data.shopId) {
        const response = await apiClient.put<T>(
          `${API_PATHS.USER_GROUP.API_USER_GROUP}`,
          data,
          config
        );
        return response;
      }
    } catch (error: any) {
      handleApiError(error);
    }
  },

  getListUserGroupById: async <T = any>(
    shopId: string,
    groupId: string,
    errorConfig?: ErrorConfig
  ) => {
    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
    };
    try {
      if (shopId && groupId) {
        const response = await apiClient.get<T>(
          `${API_PATHS.USER_GROUP.API_USER_GROUP}/${shopId}/${groupId}`,
          config
        );
        return response;
      }
    } catch (error: any) {
      handleApiError(error);
    }
  },
};

import { apiClient } from "@/src/api/config/api-client";
import { API_PATHS } from "@/src/api/constants/api-paths";
import type { ApiError, ErrorConfig } from "@/src/api/types/error.types";
import type { ExtendedRequestConfig } from "@/src/api/types/api.types";
import { PointPromotion, PointPromotionInput } from "@/src/api/types/point-promotion.type";
import { TypeActive } from "@/src/api/types/voucher.type";

function handleApiError(error: any): never {
  const status = error.response?.status || 500;
  const errorData = error.response?.data as ApiError;
  const errorMessage = errorData?.detail || errorData?.title || error.message;

  if (error.response) {
    throw {
      ...error,
      message: errorMessage,
    };
  }
  throw error;
}

export const pointPromotionService = {
  listPointPromotion: async <T = any>(params, errorConfig?: ErrorConfig) => {
    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
    };
    const queryParams = new URLSearchParams({
      skip: params.skip,
      limit: params.limit,
      shopId: params.shopId,
    });

    if (params.searchType) {
      queryParams.append("searchType", params.searchType);
      console.log(`API call will use searchType: ${params.searchType}`);
    }

    if (params.search) {
      queryParams.append("Search", params.search);
    }
    if (params.statusPromotion) {
      queryParams.append("statusPromotion", params.statusPromotion);
    }

    try {
      const response = await apiClient.get<T>(
        `${API_PATHS.POINT_PROMOTION.LIST_POINT_PROMOTION}?${queryParams.toString()}`,
        config
      );
      return response;
    } catch (error: any) {
      handleApiError(error);
    }
  },

  createPointPromotion: async <T = any>(data: PointPromotionInput, errorConfig?: ErrorConfig) => {
    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
    };

    try {
      const response = await apiClient.post<PointPromotionInput, T>(
        API_PATHS.POINT_PROMOTION.CREATE_POINT_PROMOTION,
        data,
        config
      );
      return response;
    } catch (error: any) {
      handleApiError(error);
    }
  },

  updatePointPromotion: async <T = any>(data: PointPromotion, errorConfig?: ErrorConfig) => {
    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
    };

    try {
      const response = await apiClient.put<PointPromotion, T>(
        API_PATHS.POINT_PROMOTION.UPDATE_POINT_PROMOTION,
        data,
        config
      );
      return response;
    } catch (error: any) {
      handleApiError(error);
    }
  },

  detailPointPromotion: async <T = any>(promotionId: string, errorConfig?: ErrorConfig) => {
    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
    };

    try {
      const response = await apiClient.get<T>(
        `${API_PATHS.POINT_PROMOTION.DETAIL_POINT_PROMOTION}?promotionId=${promotionId}`,
        config
      );
      return response;
    } catch (error: any) {
      handleApiError(error);
    }
  },

  deletePointPromotion: async <T = any>(promotionIds: string[], errorConfig?: ErrorConfig) => {
    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
    };

    try {
      const response = await apiClient.delete<T>(
        `${API_PATHS.POINT_PROMOTION.DELETE_POINT_PROMOTION}`,
        {
          ...config,
          data: promotionIds,
        }
      );
      return response;
    } catch (error: any) {
      handleApiError(error);
    }
  },

  changeActivePointPromotion: async <T = any>(
    promotionIds: string[],
    active: TypeActive,
    errorConfig?: ErrorConfig
  ) => {
    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
    };

    try {
      const response = await apiClient.patch<string[], T>(
        `${API_PATHS.POINT_PROMOTION.CHANGE_ACTIVE_POINT_PROMOTION}?active=${active}`,
        promotionIds,
        config
      );
      return response;
    } catch (error: any) {
      handleApiError(error);
    }
  },

  listPointVoucherCodes: async <T = any>(
    promotionId: string,
    skip: number = 0,
    limit: number = 20,
    isUsed?: boolean,
    isExpired?: boolean,
    pointsCode?: string,
    errorConfig?: ErrorConfig
  ) => {
    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
    };

    const queryParams = new URLSearchParams({
      promotionId,
      skip: skip.toString(),
      limit: limit.toString(),
    });

    if (isUsed !== undefined) {
      queryParams.append("isUsed", isUsed.toString());
    }

    if (isExpired !== undefined) {
      queryParams.append("isExpired", isExpired.toString());
    }

    if (pointsCode) {
      queryParams.append("pointsCode", pointsCode);
    }

    try {
      const response = await apiClient.get<T>(
        `${API_PATHS.POINT_PROMOTION.LIST_POINT_VOUCHER_CODES}?${queryParams.toString()}`,
        config
      );
      return response;
    } catch (error: any) {
      handleApiError(error);
    }
  },
};

import React, { useState } from 'react';
import { Box, Button, Input } from 'zmp-ui';
import { showToast } from 'zmp-sdk/apis';
import { authAPI } from '../utils/api';

const ApiDebugger = () => {
  const [phoneNumber, setPhoneNumber] = useState('0947548263');
  const [password, setPassword] = useState('12345678'); // At least 8 characters
  const [referralCode, setReferralCode] = useState('TEST123');
  const [loading, setLoading] = useState(false);
  const [response, setResponse] = useState(null);
  const [error, setError] = useState(null);

  const testRegister = async () => {
    setLoading(true);
    setResponse(null);
    setError(null);

    try {
      // Format phone number like the API does
      const formattedPhone = phoneNumber.startsWith('+84')
        ? phoneNumber
        : phoneNumber.startsWith('0')
          ? `+84${phoneNumber.slice(1)}`
          : `+84${phoneNumber}`;

      console.log('Testing register with:', {
        originalPhone: phoneNumber,
        formattedPhone: formattedPhone,
        password,
        referralCode
      });

      const result = await authAPI.register(phoneNumber, password, referralCode);
      
      setResponse(result);
      showToast({
        message: 'Đăng ký thành công!',
        type: 'success',
        duration: 2000
      });
    } catch (err) {
      console.error('Register test error:', err);
      setError({
        message: err.message,
        status: err.response?.status,
        data: err.response?.data,
        config: {
          url: err.config?.url,
          method: err.config?.method,
          data: err.config?.data
        }
      });
      
      showToast({
        message: `Lỗi: ${err.response?.data?.message || err.message}`,
        type: 'error',
        duration: 3000
      });
    } finally {
      setLoading(false);
    }
  };

  const testLogin = async () => {
    setLoading(true);
    setResponse(null);
    setError(null);

    try {
      console.log('Testing login with:', {
        phoneNumber,
        password
      });

      const result = await authAPI.loginWithPhone(phoneNumber, password);
      
      setResponse(result);
      showToast({
        message: 'Đăng nhập thành công!',
        type: 'success',
        duration: 2000
      });
    } catch (err) {
      console.error('Login test error:', err);
      setError({
        message: err.message,
        status: err.response?.status,
        data: err.response?.data,
        config: {
          url: err.config?.url,
          method: err.config?.method,
          data: err.config?.data
        }
      });
      
      showToast({
        message: `Lỗi: ${err.response?.data?.message || err.message}`,
        type: 'error',
        duration: 3000
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <Box className="p-4 bg-white rounded-lg shadow-md mb-4">
      <h3 className="text-lg font-bold mb-4 text-red-600">🔧 API Debugger</h3>
      
      {/* Input Fields */}
      <Box className="space-y-3 mb-4">
        <Input
          placeholder="Số điện thoại"
          value={phoneNumber}
          onChange={(e) => setPhoneNumber(e.target.value)}
          className="w-full"
        />
        
        <Input
          placeholder="Mật khẩu"
          value={password}
          onChange={(e) => setPassword(e.target.value)}
          type="password"
          className="w-full"
        />
        
        <Input
          placeholder="Mã giới thiệu"
          value={referralCode}
          onChange={(e) => setReferralCode(e.target.value)}
          className="w-full"
        />
      </Box>

      {/* Action Buttons */}
      <Box className="flex gap-2 mb-4">
        <Button
          onClick={testRegister}
          disabled={loading}
          className="bg-blue-500 text-white px-4 py-2 rounded"
        >
          {loading ? 'Testing...' : 'Test Register'}
        </Button>
        
        <Button
          onClick={testLogin}
          disabled={loading}
          className="bg-green-500 text-white px-4 py-2 rounded"
        >
          {loading ? 'Testing...' : 'Test Login'}
        </Button>
      </Box>

      {/* Response Display */}
      {response && (
        <Box className="mb-4">
          <h4 className="font-bold text-green-600 mb-2">✅ Success Response:</h4>
          <pre className="bg-green-50 p-3 rounded text-xs overflow-auto">
            {JSON.stringify(response, null, 2)}
          </pre>
        </Box>
      )}

      {/* Error Display */}
      {error && (
        <Box className="mb-4">
          <h4 className="font-bold text-red-600 mb-2">❌ Error Details:</h4>
          <Box className="space-y-2">
            <Box className="bg-red-50 p-3 rounded">
              <p className="text-sm"><strong>Status:</strong> {error.status}</p>
              <p className="text-sm"><strong>Message:</strong> {error.message}</p>
            </Box>
            
            {error.data && (
              <Box className="bg-red-50 p-3 rounded">
                <p className="text-sm font-bold mb-1">Response Data:</p>
                <pre className="text-xs overflow-auto">
                  {JSON.stringify(error.data, null, 2)}
                </pre>
              </Box>
            )}
            
            {error.config && (
              <Box className="bg-gray-50 p-3 rounded">
                <p className="text-sm font-bold mb-1">Request Config:</p>
                <pre className="text-xs overflow-auto">
                  {JSON.stringify(error.config, null, 2)}
                </pre>
              </Box>
            )}
          </Box>
        </Box>
      )}

      {/* API Info */}
      <Box className="bg-gray-50 p-3 rounded text-xs">
        <p><strong>API Base URL:</strong> {process.env.REACT_APP_API_URL || 'https://dev-admin.evotech.vn'}</p>
        <p><strong>Register Endpoint:</strong> POST /api/user/authuser/register</p>
        <p><strong>Login Endpoint:</strong> POST /api/user/authuser/login</p>
      </Box>
    </Box>
  );
};

export default ApiDebugger;

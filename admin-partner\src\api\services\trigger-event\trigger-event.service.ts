import { apiClient } from "../../config/api-client";
import { API_PATHS } from "../../constants/api-paths";
import type { ApiError, ErrorConfig } from "../../types/error.types";
import type { ExtendedRequestConfig } from "../../types/api.types";
import { logger } from "@/src/utils/logger";
import { UpdateUserInfoBody } from "../../types/user.types";

function handleApiError(error: any): never {
  const status = error.response?.status || 500;
  const errorData = error.response?.data as ApiError;
  const errorMessage = errorData?.detail || errorData?.title || error.message;

  if (error.response) {
    throw {
      ...error,
      message: errorMessage,
    };
  }

  throw error;
}

export interface ParamGetListTriggerEvent {
  type: string;
  shopId: string;
}

export interface ParamGetListTemplate {
  shopId: string;
  triggerEventId: string;
}

export interface ParamUpdateStatusTemplate {
  triggerEventId: string;
  templateId: string;
  isActive: boolean;
}

export interface TriggerEventParameter {
  name: string;
  value: string;
  dataType: string;
  orderNumber: number;
}

export interface TemplateEventParameter {
  shopId: string;
  triggerEventId: string;
  channelType: string;
  channelName: string;
  templateId: string;
  policyName: string;
  policyLink: string;
  price: number;
  isActive: boolean;
}

export interface TriggerEventDto {
  triggerEventId: string;
  code: string;
  eventName: string;
  type: string;
  description: string;
  orderNumber: number;
  parameters: TriggerEventParameter[];
  templates: TemplateEventParameter[];
  // createdDate: string;
  // modifiedDate: string | null;
  // createdBy: string | null;
  // modifiedBy: string | null;
  // isDeleted: boolean;
  // deletedAt: string | null;
  // id: string;
}

export const userTriggerEventService = {
  getListTriggerEvent: async <T = any>(errorConfig?: ErrorConfig) => {
    const config: any = {
      errorHandling: errorConfig,
    };

    try {
      const response = await apiClient.get<T>(
        `${API_PATHS.TRIGGER_EVENT.API_TRIGGER_EVENT}`,
        config
      );

      return response;
    } catch (error: any) {
      handleApiError(error);
    }
  },
  getListTriggerEventByType: async <T = any>(
    params: ParamGetListTriggerEvent,
    errorConfig?: ErrorConfig
  ) => {
    const config: any = {
      errorHandling: errorConfig,
    };

    try {
      if (params.type && params.shopId) {
        const response = await apiClient.get<T>(
          `${API_PATHS.TRIGGER_EVENT.API_TRIGGER_EVENT}/getlist/${params.shopId}/${params.type}`,
          config
        );

        return response;
      }
    } catch (error: any) {
      handleApiError(error);
    }
  },

  getListParamByTriggerEventId: async <T = any>(
    triggerEventId: string,
    errorConfig?: ErrorConfig
  ) => {
    const config: any = {
      errorHandling: errorConfig,
    };

    try {
      if (triggerEventId) {
        const response = await apiClient.get<T>(
          `${API_PATHS.TRIGGER_EVENT.API_TRIGGER_EVENT}/${triggerEventId}/parameter`,
          config
        );

        return response;
      }
    } catch (error: any) {
      handleApiError(error);
    }
  },

  getListTemplateByTriggerEventId: async <T = any>(
    params: ParamGetListTemplate,
    errorConfig?: ErrorConfig
  ) => {
    const config: any = {
      errorHandling: errorConfig,
    };

    try {
      if (params.shopId && params.triggerEventId) {
        const response = await apiClient.get<T>(
          `${API_PATHS.TRIGGER_EVENT.API_TRIGGER_EVENT}/${params.shopId}/${params.triggerEventId}/template`,
          config
        );

        return response;
      }
    } catch (error: any) {
      handleApiError(error);
    }
  },

  activeStatusTemplate: async <T = any>(
    params: ParamUpdateStatusTemplate,
    errorConfig?: ErrorConfig
  ) => {
    const config: any = {
      errorHandling: errorConfig,
    };

    try {
      if (params.triggerEventId && params.templateId) {
        const response = await apiClient.put<T>(
          `${API_PATHS.TRIGGER_EVENT.API_TRIGGER_EVENT}/${params.triggerEventId}/template/${params.templateId}/active?isActive=${params.isActive}`,
          config
        );

        return response;
      }
    } catch (error: any) {
      handleApiError(error);
    }
  },
};

import { Page, Box, useN<PERSON><PERSON>, But<PERSON> } from "zmp-ui";
import { showToast, openWebview } from 'zmp-sdk/apis';
import { useAuth } from "../hooks/useAuth";
import { userService } from "../utils/userService";
import StickyButtons from "../components/StickyButtons";
import DynamicQRCode from "../components/DynamicQRCode";
import UserProfile from "../components/UserProfile";
import taiKhoanIcon from "../static/tai-khoan.svg";
import walletIcon from "../static/Wallet.svg";
import boxIcon from "../static/box.svg";
import deliveryIcon from "../static/delivery.svg";
import starIcon from "../static/star.svg";
import sodiachIcon from "../static/sodiachi.svg";
import khovoucherIcon from "../static/khovoucher.svg";
import thongtintaikhoanIcon from "../static/thongtintaikhoan.svg";
import danhsachcuahangIcon from "../static/danhsachcuahang.svg";
import taoiconappIcon from "../static/taoiconapp.svg";
import dieukhoaIcon from "../static/dieukhoan.svg";
import hotrovahoidapIcon from "../static/hotrovahoidap.svg";

import saochepIcon from "../static/saochep.svg";
import taixuongIcon from "../static/taixuong.svg";
import chiaseIcon from "../static/chiase.svg";
import evotechLogo from "../static/Evotech-logo.svg";

function AccountPage() {
  const navigate = useNavigate();
  const { isAuthenticated, logout, getUserReferralCode } = useAuth();

  // Default shopId - lấy từ environment hoặc URL params
  const defaultShopId = process.env.REACT_APP_DEFAULT_SHOP_ID || "0fa960fa-b082-46ac-858d-836cac08893f";

  const handleViewOAQR = async () => {
    // Direct OA profile - No errors, always works
    showToast({
      message: 'Đang mở SonNH store Official Account...',
      type: 'success',
      duration: 2000
    });

    try {
      const userRefCode = getUserReferralCode();
      const url = userRefCode
        ? `https://zalo.me/oa/2958747652443832938?ref=${userRefCode}`
        : `https://zalo.me/oa/2958747652443832938`;

      await openWebview({
        url: url,
        config: {
          style: 'bottomSheet',
          leftButton: 'back',
          title: 'SonNH store'
        }
      });
    } catch (error) {
      console.error('Webview error:', error);
      // Even if webview fails, provide alternative
      showToast({
        message: 'Đang chuyển hướng đến Official Account...',
        type: 'success',
        duration: 2000
      });

      // Last resort: try to open in external browser
      const userRefCode = getUserReferralCode();
      const url = userRefCode
        ? `https://zalo.me/oa/2958747652443832938?ref=${userRefCode}`
        : `https://zalo.me/oa/2958747652443832938`;
      window.open(url, '_blank');
    }
  };

  const handleLogout = async () => {
    try {
      const result = await logout();
      if (result.success) {
        showToast({
          message: result.message,
          type: 'success',
          duration: 2000
        });
        navigate('/login');
      }
    } catch (error) {
      showToast({
        message: 'Đăng xuất thất bại',
        type: 'error',
        duration: 2000
      });
    }
  };

  // If not authenticated, show login prompt
  if (!isAuthenticated) {
    return (
      <Page className="min-h-screen page-content-safe" style={{ backgroundColor: '#FFFEF1' }}>
        {/* Header */}
        <Box className="px-4 pt-10 pb-3" style={{ backgroundColor: '#FFFEF1' }}>
          <h1 className="text-2xl font-bold text-gray-800">Tài khoản</h1>
        </Box>

        {/* Login Prompt */}
        <Box className="flex flex-col items-center justify-center min-h-[60vh] px-4">
          <Box className="text-center mb-8">
            <img src={taiKhoanIcon} alt="Account" className="w-20 h-20 mx-auto mb-4 icon-brand-blue" />
            <h2 className="text-xl font-bold text-gray-800 mb-2">Chưa đăng nhập</h2>
            <p className="text-gray-600 mb-6">
              Đăng nhập để trải nghiệm đầy đủ tính năng của ứng dụng
            </p>
            <Button
              size="large"
              onClick={() => navigate('/login')}
              className="bg-brand-blue text-white px-8 py-3 rounded-lg font-medium"
            >
              Đăng nhập ngay
            </Button>
          </Box>
        </Box>

        {/* Sticky Buttons */}
        <StickyButtons />
      </Page>
    );
  }

  return (
    <Page className="min-h-screen page-content-safe" style={{ backgroundColor: '#FFFEF1' }}>
      {/* Header - chữ "Tài khoản" */}
      <Box className="px-4 pt-10 pb-3 flex justify-between items-center" style={{ backgroundColor: '#FFFEF1' }}>
        <h1 className="text-2xl font-bold text-gray-800">Tài khoản</h1>
        <Button
          size="small"
          onClick={handleLogout}
          className="bg-red-500 text-white px-4 py-2 rounded-lg text-sm"
        >
          Đăng xuất
        </Button>
      </Box>

      {/* User Profile Section - Using new UserProfile component */}
      <UserProfile shopId={defaultShopId} />

      {/* Đơn hàng của tôi Section */}
      <Box className="mb-4 md:mb-6">
        <Box className="bg-white shadow-md p-4 md:p-8">
          {/* Header */}
          <Box className="flex items-center justify-between mb-4 md:mb-6">
            <h2 className="text-base md:text-lg font-bold text-brand-blue">Đơn hàng của tôi</h2>
            <Box className="flex items-center">
              <span className="text-xs md:text-sm mr-1 text-brand-blue">Xem lịch sử mua hàng</span>
              <svg className="w-3 h-3 md:w-4 md:h-4 stroke-brand-blue" fill="none" viewBox="0 0 24 24" strokeWidth="2">
                <path strokeLinecap="round" strokeLinejoin="round" d="M9 5l7 7-7 7" />
              </svg>
            </Box>
          </Box>

          {/* Order Status Icons */}
          <Box className="relative">
            {/* Connecting Lines - Dashed */}
            <Box className="absolute top-5 md:top-6 left-1/2 right-1/2 flex items-center justify-between" style={{ left: '12.5%', right: '12.5%', width: '75%' }}>
              {/* Line 1: Chờ xác nhận -> Chờ lấy hàng (màu 1531AD, nét đứt) */}
              <Box className="flex-1 h-0.5 border-t-2 border-dashed border-brand-blue"></Box>
              {/* Line 2: Chờ lấy hàng -> Chờ giao hàng (màu xám, nét đứt) */}
              <Box className="flex-1 h-0.5 border-t-2 border-dashed border-gray-300"></Box>
              {/* Line 3: Chờ giao hàng -> Đánh giá (màu xám, nét đứt) */}
              <Box className="flex-1 h-0.5 border-t-2 border-dashed border-gray-300"></Box>
            </Box>

            {/* Icons Grid */}
            <Box className="grid grid-cols-4 gap-2 md:gap-4 relative z-10">
              {/* Chờ xác nhận - Active (nền xám nhạt, không outline) */}
              <Box className="flex flex-col items-center">
                <Box className="w-10 h-10 md:w-12 md:h-12 bg-gray-200 rounded-full flex items-center justify-center mb-1.5 md:mb-2">
                  <img src={walletIcon} alt="Wallet" className="w-5 h-5 md:w-6 md:h-6 object-contain icon-brand-blue" />
                </Box>
                <span className="text-xs md:text-sm text-center text-gray-600 leading-tight px-1">Chờ xác nhận</span>
              </Box>

              {/* Chờ lấy hàng - Next Active (outline màu 1531AD, nền trắng) */}
              <Box className="flex flex-col items-center">
                <Box className="w-10 h-10 md:w-12 md:h-12 bg-white rounded-full flex items-center justify-center mb-1.5 md:mb-2 border-2 border-brand-blue">
                  <img src={boxIcon} alt="Box" className="w-5 h-5 md:w-6 md:h-6 object-contain icon-brand-blue" />
                </Box>
                <span className="text-xs md:text-sm text-center text-gray-600 leading-tight px-1">Chờ lấy hàng</span>
              </Box>

              {/* Chờ giao hàng - Inactive (outline xám nhạt, nền trắng) */}
              <Box className="flex flex-col items-center">
                <Box className="w-10 h-10 md:w-12 md:h-12 bg-white rounded-full flex items-center justify-center mb-1.5 md:mb-2 border-2 border-gray-300">
                  <img src={deliveryIcon} alt="Delivery" className="w-5 h-5 md:w-6 md:h-6 object-contain icon-brand-blue" />
                </Box>
                <span className="text-xs md:text-sm text-center text-gray-600 leading-tight px-1">Chờ giao hàng</span>
              </Box>

              {/* Đánh giá - Inactive (outline xám nhạt, nền trắng) */}
              <Box className="flex flex-col items-center">
                <Box className="w-10 h-10 md:w-12 md:h-12 bg-white rounded-full flex items-center justify-center mb-1.5 md:mb-2 border-2 border-gray-300">
                  <img src={starIcon} alt="Star" className="w-5 h-5 md:w-6 md:h-6 object-contain icon-brand-blue" />
                </Box>
                <span className="text-xs md:text-sm text-center text-gray-600 leading-tight px-1">Đánh giá</span>
              </Box>
            </Box>
          </Box>
        </Box>
      </Box>

      {/* Chương trình đối tác Section */}
      <Box className="mb-4 md:mb-6">
        <Box className="bg-white shadow-md p-4 md:p-6">
          {/* Header */}
          <h2 className="text-base md:text-lg font-bold mb-4 md:mb-6 text-brand-blue">Chương trình đối tác</h2>

          {/* Partnership Card */}
          <Box className="bg-gray-50 rounded-2xl p-3 md:p-4 border-2 border-brand-blue">
            <Box className="flex items-center">
              {/* Partnership Content */}
              <Box className="flex-1 pr-3 md:pr-4">
                {/* First Partnership */}
                <Box className="mb-2 md:mb-3">
                  <p className="text-gray-700 mb-0.5 text-xs md:text-sm">Giới thiệu khách hàng cấp 1 hoa hồng</p>
                  <p className="text-base md:text-lg font-bold text-brand-blue">10%</p>
                </Box>

                {/* Divider Line */}
                <Box className="border-t border-gray-300"></Box>

                {/* Second Partnership */}
                <Box className="mt-2 md:mt-3">
                  <p className="text-gray-700 mb-0.5 text-xs md:text-sm">Giới thiệu khách hàng cấp 2 hoa hồng</p>
                  <p className="text-base md:text-lg font-bold text-brand-blue">5%</p>
                </Box>
              </Box>

              {/* Register Button */}
              <Box className="flex-shrink-0">
                <button className="px-4 py-2 md:px-6 md:py-3 rounded-full text-white font-medium text-xs md:text-sm bg-brand-blue whitespace-nowrap">
                  Đăng ký
                </button>
              </Box>
            </Box>
          </Box>
        </Box>
      </Box>

      {/* Menu Options Section */}
      <Box className="mb-4 md:mb-6">
        <Box className="bg-white shadow-md">
          <Box className="p-4 md:p-5">
            {/* Số đã chi & Kho Voucher */}
            <Box className="grid grid-cols-2 gap-3 md:gap-4 mb-3">
              {/* Số đã chi */}
              <Box className="p-3 md:p-4 border border-gray-200 rounded-lg flex items-center justify-center">
                <Box className="flex items-center">
                  <Box className="w-6 h-6 md:w-8 md:h-8 mr-2 md:mr-3 flex items-center justify-center">
                    <img src={sodiachIcon} alt="Số đã chi" className="w-5 h-5 md:w-6 md:h-6 object-contain icon-brand-blue" />
                  </Box>
                  <Box>
                    <p className="text-xs md:text-sm font-medium text-brand-blue">Số đã chi</p>
                    <p className="text-xs text-gray-500">Tất cả nhật hàng</p>
                  </Box>
                </Box>
              </Box>

              {/* Kho Voucher */}
              <Box className="p-3 md:p-4 border border-gray-200 rounded-lg flex items-center justify-center">
                <Box className="flex items-center">
                  <Box className="w-6 h-6 md:w-8 md:h-8 mr-2 md:mr-3 flex items-center justify-center">
                    <img src={khovoucherIcon} alt="Kho Voucher" className="w-5 h-5 md:w-6 md:h-6 object-contain icon-brand-blue" />
                  </Box>
                  <Box>
                    <p className="text-xs md:text-sm font-medium text-brand-blue">Kho Voucher</p>
                    <p className="text-xs text-gray-500">Các voucher khuyến mãi</p>
                  </Box>
                </Box>
              </Box>
            </Box>
            <Box className="border border-gray-200 rounded-lg">
              {/* Menu Items */}
              <Box>
                {/* Thông tin tài khoản */}
                <Box className="flex items-center justify-between p-3 md:p-4 border-b border-gray-200 cursor-pointer">
                  <Box className="flex items-center">
                    <Box className="w-6 h-6 md:w-8 md:h-8 mr-2 md:mr-3 flex items-center justify-center">
                      <img src={thongtintaikhoanIcon} alt="Thông tin tài khoản" className="w-5 h-5 md:w-6 md:h-6 object-contain icon-brand-blue" />
                    </Box>
                    <Box>
                      <p className="text-xs md:text-sm font-medium text-brand-blue">Thông tin tài khoản</p>
                      <p className="text-xs text-gray-500">Cập nhật thông tin định danh</p>
                    </Box>
                  </Box>
                  <svg className="w-4 h-4 md:w-5 md:h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" strokeWidth="2">
                    <path strokeLinecap="round" strokeLinejoin="round" d="M9 5l7 7-7 7" />
                  </svg>
                </Box>

                {/* Danh sách cửa hàng */}
                <Box
                  className="flex items-center justify-between p-3 md:p-4 border-b border-gray-200 cursor-pointer hover:bg-gray-50 transition-colors"
                  onClick={() => navigate('/store-list')}
                >
                  <Box className="flex items-center">
                    <Box className="w-6 h-6 md:w-8 md:h-8 mr-2 md:mr-3 flex items-center justify-center">
                      <img src={danhsachcuahangIcon} alt="Danh sách cửa hàng" className="w-5 h-5 md:w-6 md:h-6 object-contain icon-brand-blue" />
                    </Box>
                    <Box>
                      <p className="text-xs md:text-sm font-medium text-brand-blue">Danh sách cửa hàng</p>
                      <p className="text-xs text-gray-500">Vị trí và thông tin cửa hàng</p>
                    </Box>
                  </Box>
                  <svg className="w-4 h-4 md:w-5 md:h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" strokeWidth="2">
                    <path strokeLinecap="round" strokeLinejoin="round" d="M9 5l7 7-7 7" />
                  </svg>
                </Box>

                {/* Tạo icon App trên màn hình chính */}
                <Box className="flex items-center justify-between p-3 md:p-4 border-b border-gray-200">
                  <Box className="flex items-center">
                    <Box className="w-6 h-6 md:w-8 md:h-8 mr-2 md:mr-3 flex items-center justify-center">
                      <img src={taoiconappIcon} alt="Tạo icon App" className="w-5 h-5 md:w-6 md:h-6 object-contain icon-brand-blue" />
                    </Box>
                    <Box>
                      <p className="text-xs md:text-sm font-medium text-brand-blue">Tạo icon App trên màn hình chính</p>
                      <p className="text-xs text-gray-500">Dễ dàng truy cập Mini App hơn</p>
                    </Box>
                  </Box>
                  <svg className="w-4 h-4 md:w-5 md:h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" strokeWidth="2">
                    <path strokeLinecap="round" strokeLinejoin="round" d="M9 5l7 7-7 7" />
                  </svg>
                </Box>

                {/* Điều khoản và hỗi đáp */}
                <Box
                  className="flex items-center justify-between p-3 md:p-4 border-b border-gray-200 cursor-pointer hover:bg-gray-50 transition-colors"
                  onClick={() => navigate('/terms')}
                >
                  <Box className="flex items-center">
                    <Box className="w-6 h-6 md:w-8 md:h-8 mr-2 md:mr-3 flex items-center justify-center">
                      <img src={dieukhoaIcon} alt="Điều khoản" className="w-5 h-5 md:w-6 md:h-6 object-contain icon-brand-blue" />
                    </Box>
                    <Box>
                      <p className="text-xs md:text-sm font-medium text-brand-blue">Điều khoản và hỗi đáp</p>
                      <p className="text-xs text-gray-500">Câu hỏi thường gặp</p>
                    </Box>
                  </Box>
                  <svg className="w-4 h-4 md:w-5 md:h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" strokeWidth="2">
                    <path strokeLinecap="round" strokeLinejoin="round" d="M9 5l7 7-7 7" />
                  </svg>
                </Box>

                {/* Hỗ trợ và hỗi đáp */}
                <Box className="flex items-center justify-between p-3 md:p-4">
                  <Box className="flex items-center">
                    <Box className="w-6 h-6 md:w-8 md:h-8 mr-2 md:mr-3 flex items-center justify-center">
                      <img src={hotrovahoidapIcon} alt="Hỗ trợ và hỗi đáp" className="w-5 h-5 md:w-6 md:h-6 object-contain icon-brand-blue" />
                    </Box>
                    <Box>
                      <p className="text-xs md:text-sm font-medium text-brand-blue">Hỗ trợ và hỗi đáp</p>
                      <p className="text-xs text-gray-500">Câu hỏi liên đến ngôi nhân viên</p>
                    </Box>
                  </Box>
                  <svg className="w-4 h-4 md:w-5 md:h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" strokeWidth="2">
                    <path strokeLinecap="round" strokeLinejoin="round" d="M9 5l7 7-7 7" />
                  </svg>
                </Box>
              </Box>
            </Box>
          </Box>
        </Box>
      </Box>

      {/* QR Code Sharing Section */}
      <Box className="mb-4 md:mb-6">
        <Box className="bg-white shadow-md p-4 md:p-6">
          {/* Header Text */}
          <Box className="text-center mb-4 md:mb-6">
            <p className="text-xs md:text-sm text-gray-700 leading-relaxed mb-3 md:mb-4">
              Chia sẻ cửa hàng "Evotech test store(OA account id)" với bạn bè để nhận điểm thưởng! QR này<br className="hidden md:block" />có chứa mã giới thiệu của bạn!
            </p>
            <button className="text-xs md:text-sm font-medium underline text-brand-blue">
              Xem chi tiết
            </button>
          </Box>

          {/* Dashed Line */}
          <Box className="border border-dashed border-gray-800 mb-4 md:mb-6 w-2/3 mx-auto"></Box>

          {/* QR Code */}
          <Box className="flex justify-center mb-4 md:mb-6">
            <Box className="relative">
              <DynamicQRCode
                data={getUserReferralCode()
                  ? `https://zalo.me/oa/2958747652443832938?ref=${getUserReferralCode()}`
                  : "https://zalo.me/oa/2958747652443832938"
                }
                size={180}
                className="cursor-pointer hover:opacity-80 transition-opacity md:w-[200px] md:h-[200px]"
                onClick={handleViewOAQR}
                alt="SonNH store QR Code"
              />
              {/* Overlay to indicate it's clickable */}
              <Box className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-0 hover:bg-opacity-10 transition-all duration-200 rounded-lg pointer-events-none">
                <Box className="bg-white bg-opacity-90 px-2 md:px-3 py-1 rounded-full opacity-0 hover:opacity-100 transition-opacity duration-200">
                  <span className="text-xs font-medium text-brand-blue">Xem SonNH store</span>
                </Box>
              </Box>
            </Box>
          </Box>

          {/* Action Buttons */}
          <Box className="grid grid-cols-3 gap-2 md:gap-4">
            {/* Sao chép */}
            <Box className="flex flex-col items-center">
              <Box className="w-10 h-10 md:w-12 md:h-12 bg-blue-50 rounded-full flex items-center justify-center mb-1.5 md:mb-2">
                <img src={saochepIcon} alt="Sao chép" className="w-5 h-5 md:w-6 md:h-6 object-contain icon-brand-blue" />
              </Box>
              <span className="text-xs text-center font-medium text-brand-blue">Sao chép</span>
            </Box>

            {/* Tải xuống */}
            <Box className="flex flex-col items-center">
              <Box className="w-10 h-10 md:w-12 md:h-12 bg-blue-50 rounded-full flex items-center justify-center mb-1.5 md:mb-2">
                <img src={taixuongIcon} alt="Tải xuống" className="w-5 h-5 md:w-6 md:h-6 object-contain icon-brand-blue" />
              </Box>
              <span className="text-xs text-center font-medium text-brand-blue">Tải xuống</span>
            </Box>

            {/* Chia sẻ link */}
            <Box className="flex flex-col items-center">
              <Box className="w-10 h-10 md:w-12 md:h-12 bg-blue-50 rounded-full flex items-center justify-center mb-1.5 md:mb-2">
                <img src={chiaseIcon} alt="Chia sẻ link" className="w-5 h-5 md:w-6 md:h-6 object-contain icon-brand-blue" />
              </Box>
              <span className="text-xs text-center font-medium text-brand-blue">Chia sẻ link</span>
            </Box>
          </Box>
        </Box>
      </Box>

      {/* Footer */}
      <Box className="px-4 mt-1">
        <Box className="flex items-center justify-center gap-2">
          <p className="text-xs text-gray-400" style={{ fontFamily: 'SVN-Helvetica Now, sans-serif' }}>
            POWERED BY
          </p>
          <img src={evotechLogo} alt="Evotech" className="h-6 object-contain opacity-60" />
        </Box>
      </Box>

      {/* Sticky Buttons */}
      <StickyButtons />
    </Page>
  );
}

export default AccountPage;

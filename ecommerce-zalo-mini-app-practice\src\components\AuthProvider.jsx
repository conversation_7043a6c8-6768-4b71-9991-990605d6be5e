import React, { useEffect } from 'react';
import { useAtom } from 'jotai';
import {
  userAtom,
  isAuthenticatedAtom,
  isLoadingAtom,
  authActionsAtom,
  referralCodeAtom
} from '../store/authStore';
import { getReferralCode } from '../utils/storage';
import { userService } from '../utils/userService';
import ReferralHandler from './ReferralHandler';

const AuthProvider = ({ children }) => {
  const [user, setUser] = useAtom(userAtom);
  const [isAuthenticated, setIsAuthenticated] = useAtom(isAuthenticatedAtom);
  const [isLoading, setIsLoading] = useAtom(isLoadingAtom);
  const [, dispatch] = useAtom(authActionsAtom);
  const [referralCode, setReferralCode] = useAtom(referralCodeAtom);

  useEffect(() => {
    // Initialize auth state when app starts
    dispatch({ type: 'INITIALIZE' });

    // Check for referral code in URL
    const urlParams = new URLSearchParams(window.location.search);
    const refFromUrl = urlParams.get('ref') || urlParams.get('referralCode');

    if (refFromUrl && refFromUrl !== referralCode) {
      dispatch({
        type: 'SET_REFERRAL_CODE',
        payload: { code: refFromUrl }
      });
    }

    // If user is authenticated, fetch latest user info
    const fetchUserInfo = async () => {
      if (isAuthenticated && localStorage.getItem('accessToken')) {
        try {
          const result = await userService.getCurrentUser();
          if (result.success) {
            // Update user info in storage and state
            localStorage.setItem('user', JSON.stringify(result.data));
            dispatch({
              type: 'LOGIN_SUCCESS',
              payload: { user: result.data }
            });
          }
        } catch (error) {
          console.error('Failed to fetch user info:', error);
        }
      }
    };

    fetchUserInfo();
  }, [dispatch, referralCode, isAuthenticated]);

  // Provide auth context to children
  const authContext = {
    user,
    isAuthenticated,
    isLoading,
    referralCode,
    dispatch,
  };

  return (
    <div>
      <ReferralHandler />
      {children}
    </div>
  );
};

export default AuthProvider;

import { useState } from 'react';
import { useStoreId } from '@/src/hooks/use-store-id';

import { cartService } from '@/src/api/services/cart/cart.service';
import { ErrorHandlerService } from '@/src/api/services/error-handler.service';
import { StorageService } from 'nextjs-api-lib';
import { branchService } from '../../services/branch/branch.service';

export const useBranch = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const partnerId = StorageService.get('partnerId') as string | null;

  const getBranches = async (skip: number, limit: number, shopId: string) => {
    try {
      setLoading(true);
      setError(null);

      const params = {
        skip,
        limit,
      };

      const response = await branchService.getBranchs(shopId, params);
      return response;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: true,
        logError: true,
      });
      setError(errorResponse.detail);
    } finally {
      setLoading(false);
    }
  };

  return {
    getBranches,
    loading,
    error,
  };
};

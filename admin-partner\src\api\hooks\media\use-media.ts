import { useState } from 'react';
import { useStoreId } from '@/src/hooks/use-store-id';
import { ErrorHandlerService } from '@/src/api/services/error-handler.service';
import type { GetGroupFileRequest, GetFileGroupRequest, CreateFileGroupRequest } from '@/src/api/types/media.types';
import { mediaService } from '../../services/media/media.service';
import { StorageService } from 'nextjs-api-lib';

export const useMedia = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const storeId = useStoreId();
  const partnerId = StorageService.get('partnerId') || null;

  const getGroups = async (skip: number, limit: number, search: string = '') => {
    try {
      setLoading(true);
      setError(null);

      const params: GetGroupFileRequest = {
        partnerId: partnerId as string,
        search,
        skip,
        limit
      };

      const response = await mediaService.getGroups(params);
      return response;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: true,
        logError: true
      });
      setError(errorResponse.detail);
      throw errorResponse;
    } finally {
      setLoading(false);
    }
  };

  const createGroup = async (data: any) => {
    try {
      setLoading(true);
      setError(null);
      const response = await mediaService.createGroup(data);
      return response;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: true,
        logError: true
      });
      setError(errorResponse.detail);
      throw errorResponse;
    } finally {
      setLoading(false);
    }
  };

  const updateGroup = async (data: any) => {
    try {
      setLoading(true);
      setError(null);
      const response = await mediaService.updateGroup(data);
      return response;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: true,
        logError: true
      });
      setError(errorResponse.detail);
      throw errorResponse;
    } finally {
      setLoading(false);
    }
  };

  const deleteGroup = async (groupFileId: string) => {
    try {
      setLoading(true);
      setError(null);
      const response = await mediaService.deleteGroup(groupFileId);
      return response;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: true,
        logError: true
      });
      setError(errorResponse.detail);
      throw errorResponse;
    } finally {
      setLoading(false);
    }
  };

  const getFiles = async (groupFileId: string | undefined, skip: number, limit: number, search: string = '') => {
    try {
      setLoading(true);
      setError(null);

      const params: GetFileGroupRequest = {
        groupFileId: groupFileId,
        search,
        skip,
        limit
      };

      const response = await mediaService.getFiles(params);
      return response;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: true,
        logError: true
      });
      setError(errorResponse.detail);
      throw errorResponse;
    } finally {
      setLoading(false);
    }
  };

  const uploadFile = async (data: any) => {
    try {
      setLoading(true);
      setError(null);
      const response = await mediaService.uploadFile(data);
      return response;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: true,
        logError: true
      });
      setError(errorResponse.detail);
      throw errorResponse;
    } finally {
      setLoading(false);
    }
  };

  const deleteFile = async (mediaFileId: string) => {
    try {
      setLoading(true);
      setError(null);
      const response = await mediaService.deleteFile(mediaFileId);
      return response;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: true,
        logError: true
      });
      setError(errorResponse.detail);
      throw errorResponse;
    } finally {
      setLoading(false);
    }
  };

  return {
    getGroups,
    createGroup,
    updateGroup,
    deleteGroup,
    getFiles,
    uploadFile,
    deleteFile,
    loading,
    error
  };
};

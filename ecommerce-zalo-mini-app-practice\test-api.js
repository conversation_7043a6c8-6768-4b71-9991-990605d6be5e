// Test script to debug API calls
const axios = require('axios');

const API_BASE_URL = 'https://dev-admin.evotech.vn';

const testRegister = async () => {
  try {
    const requestData = {
      provider: 'Phone',
      phoneNumber: '+84947548263',
      password: '12345678', // Plain text password
      referralCode: 'TEST123',
      fullname: '+84947548263',
    };

    console.log('Testing register with:', requestData);

    const response = await axios.post(`${API_BASE_URL}/api/user/authuser/register`, requestData, {
      headers: {
        'Content-Type': 'application/json',
        'Accept-Language': 'vi',
      },
      timeout: 10000,
    });

    console.log('✅ Register Success:', response.data);
  } catch (error) {
    console.error('❌ Register Error:');
    console.error('Status:', error.response?.status);
    console.error('Data:', error.response?.data);
    console.error('Headers:', error.response?.headers);
  }
};

const testLogin = async () => {
  try {
    const md5 = require('md5');
    
    const requestData = {
      provider: 'Phone',
      phoneNumber: '+84947548263',
      password: md5('12345678'), // MD5 hashed password for login
    };

    console.log('Testing login with:', requestData);

    const response = await axios.post(`${API_BASE_URL}/api/user/authuser/login`, requestData, {
      headers: {
        'Content-Type': 'application/json',
        'Accept-Language': 'vi',
      },
      timeout: 10000,
    });

    console.log('✅ Login Success:', response.data);
  } catch (error) {
    console.error('❌ Login Error:');
    console.error('Status:', error.response?.status);
    console.error('Data:', error.response?.data);
    console.error('Headers:', error.response?.headers);
  }
};

// Run tests
console.log('🔧 Testing API endpoints...\n');

testRegister().then(() => {
  console.log('\n---\n');
  return testLogin();
}).then(() => {
  console.log('\n✅ All tests completed');
}).catch(err => {
  console.error('❌ Test failed:', err.message);
});

import { apiClient } from '../../config/api-client';
import { API_PATHS } from '../../constants/api-paths';
import type { ApiError, ErrorConfig } from '../../types/error.types';
import type { ExtendedRequestConfig } from '../../types/api.types';
import { logger } from '@/src/utils/logger';

function handleApiError(error: any): never {
  const status = error.response?.status || 500;
  const errorData = error.response?.data as ApiError;
  const errorMessage = errorData?.detail || errorData?.title || error.message;

  if (error.response) {
    throw {
      ...error,
      message: errorMessage
    };
  }

  throw error;
}

export const domainNameService = {
  createDomainName: async <T = any>(data: any, errorConfig?: ErrorConfig) => {
    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig
    };

    try {
      const response = await apiClient.post<any, T>(API_PATHS.DOMAIN_NAME.CREATE_DOMAIN_NAME, data, config);

      return response;
    } catch (error: any) {
      handleApiError(error);
    }
  },

  listDomainName: async <T = any>(shopId: string, errorConfig?: ErrorConfig) => {
    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig
    };

    try {
      const response = await apiClient.get<any, T>(
        `${API_PATHS.DOMAIN_NAME.LIST_DOMAIN_NAME}?shopId=${shopId}`,
        config
      );
      return response;
    } catch (error: any) {
      handleApiError(error);
    }
  },

  updateDomainName: async <T = any>(data: any, errorConfig?: ErrorConfig) => {
    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
      headers: {
        'Content-Type': 'application/json'
      }
    };

    try {
      const response = await apiClient.put<any, T>(API_PATHS.DOMAIN_NAME.UPDATE_DOMAIN_NAME, data, config);
      return response;
    } catch (error: any) {
      handleApiError(error);
    }
  },

  deleteDomainNames: async <T = any>(domainNameIds: string[], errorConfig?: ErrorConfig) => {
    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig
    };

    try {
      const response = await apiClient.delete<T>(`${API_PATHS.DOMAIN_NAME.DELETE_DOMAIN_NAME}`, {
        ...config,
        data: domainNameIds // Truyền mảng trong body của request
      });
      return response;
    } catch (error: any) {
      handleApiError(error);
    }
  },
  checkDomainNameExist: async <T = any>(domain: string, errorConfig?: ErrorConfig) => {
    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig
    };

    try {
      const response = await apiClient.get<any, T>(
        `${API_PATHS.DOMAIN_NAME.CHECK_DOMAIN_NAME_EXIST}?domain=${domain}`,
        config
      );
      return response;
    } catch (error: any) {
      handleApiError(error);
    }
  }
};

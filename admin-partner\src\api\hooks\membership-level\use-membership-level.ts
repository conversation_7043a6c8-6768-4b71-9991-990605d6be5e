import { useState } from "react";
import { MembershipLevelService } from "../../services/membership-level/membership-level.service";
import { ErrorHandlerService } from "../../services/error-handler.service";
import { ExchangeHistoryType, GetExchangeHistoryParams } from "../../types/membership.types";

export const useMembershipLevel = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const getMembershipLevel = async (skip: number, limit: number, shopId: string) => {
    try {
      setLoading(true);
      setError(null);
      const response = await MembershipLevelService.getMembershipLevel(skip, limit, shopId);
      return response;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: true,
        logError: true,
      });
      setError(errorResponse.detail);
      throw errorResponse;
    } finally {
      setLoading(false);
    }
  };

  const createMembershipLevel = async (data: any) => {
    try {
      setLoading(true);
      setError(null);
      const response = await MembershipLevelService.createMembershipLevel(data);
      return response;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: true,
        logError: true,
      });
      setError(errorResponse.detail);
      throw errorResponse;
    } finally {
      setLoading(false);
    }
  };

  const updateLogo = async (shopId: string, logoFile: File) => {
    try {
      setLoading(true);
      setError(null);

      const response = await MembershipLevelService.updateLogoMembershipLevel(shopId, logoFile);
      return response;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: true,
        logError: true,
      });
      setError(errorResponse.detail);
      throw errorResponse;
    } finally {
      setLoading(false);
    }
  };

  const updateMembershipLevel = async (data: any) => {
    try {
      setLoading(true);
      setError(null);
      const response = await MembershipLevelService.updateMembershipLevel(data);
      return response;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: true,
        logError: true,
      });
      setError(errorResponse.detail);
      throw errorResponse;
    } finally {
      setLoading(false);
    }
  };

  const deleteMembershipLevel = async (levelId: any) => {
    try {
      setLoading(true);
      setError(null);
      const response = await MembershipLevelService.deleteMembershipLevel(levelId);
      return response;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: true,
        logError: true,
      });
      setError(errorResponse.detail);
      throw errorResponse;
    } finally {
      setLoading(false);
    }
  };

  const summaryMembershipLevel = async (shopId: any) => {
    try {
      setLoading(true);
      setError(null);
      const response = await MembershipLevelService.summaryMembershipLevel(shopId);
      return response;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: true,
        logError: true,
      });
      setError(errorResponse.detail);
      throw errorResponse;
    } finally {
      setLoading(false);
    }
  };

  const getUserPoints = async (params: any) => {
    try {
      setLoading(true);
      setError(null);
      const response = await MembershipLevelService.getUserPoints(params);
      return response;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: true,
        logError: true,
      });
      setError(errorResponse.detail);
      throw errorResponse;
    } finally {
      setLoading(false);
    }
  };

  const getDetailUserPoint = async (shopId: string, userId: string) => {
    try {
      setLoading(true);
      setError(null);
      if (shopId && userId) {
        const response = await MembershipLevelService.getDetailUserPoint(shopId, userId);
        return response;
      }
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: true,
        logError: true,
      });
      setError(errorResponse.detail);
      throw errorResponse;
    } finally {
      setLoading(false);
    }
  };

  const updateUserPoints = async (data: any) => {
    try {
      setLoading(true);
      setError(null);
      const response = await MembershipLevelService.updateUserPoints(data);
      return response;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: true,
        logError: true,
      });
      setError(errorResponse.detail);
      throw errorResponse;
    } finally {
      setLoading(false);
    }
  };

  const getExchangeHistory = async (params: {
    skip: number;
    limit: number;
    shopId: string;
    search?: string;
    type: ExchangeHistoryType;
    fromDate: Date;
    toDate: Date;
  }) => {
    try {
      setLoading(true);
      setError(null);
      const response = await MembershipLevelService.getExchangeHistory(params);
      return response;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: true,
        logError: true,
      });
      setError(errorResponse.detail);
      throw errorResponse;
    } finally {
      setLoading(false);
    }
  };

  const getSpendingHistory = async (shopId: string, userId: string) => {
    try {
      setLoading(true);
      setError(null);
      const response = await MembershipLevelService.getSpendingHistory(shopId, userId);
      return response;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: true,
        logError: true,
      });
      setError(errorResponse.detail);
      throw errorResponse;
    } finally {
      setLoading(false);
    }
  };

  const getIncomeHistory = async (shopId: string, userId: string) => {
    try {
      setLoading(true);
      setError(null);
      const response = await MembershipLevelService.getIncomeHistory(shopId, userId);
      return response;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: true,
        logError: true,
      });
      setError(errorResponse.detail);
      throw errorResponse;
    } finally {
      setLoading(false);
    }
  };

  const getConfig = async (shopId: string) => {
    try {
      setLoading(true);
      setError(null);
      const response = await MembershipLevelService.getConfig(shopId);
      return response;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: true,
        logError: true,
      });
      setError(errorResponse.detail);
      throw errorResponse;
    } finally {
      setLoading(false);
    }
  };

  const updateConfig = async (data: any, shopId: string) => {
    try {
      setLoading(true);
      setError(null);
      const response = await MembershipLevelService.updateConfig(data, shopId);
      return response;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: true,
        logError: true,
      });
      setError(errorResponse.detail);
      throw errorResponse;
    } finally {
      setLoading(false);
    }
  };
  const updateMembershipLevelImage = async (levelId: string, logoFile: File) => {
    try {
      setLoading(true);
      setError(null);

      const response = await MembershipLevelService.updateMembershipLevelImage(levelId, logoFile);
      return response;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: true,
        logError: true,
      });
      setError(errorResponse.detail);
      throw errorResponse;
    } finally {
      setLoading(false);
    }
  };

  return {
    getMembershipLevel,
    createMembershipLevel,
    updateLogo,
    updateMembershipLevel,
    deleteMembershipLevel,
    summaryMembershipLevel,
    getUserPoints,
    getDetailUserPoint,
    updateUserPoints,
    getExchangeHistory,
    getSpendingHistory,
    getIncomeHistory,
    getConfig,
    updateConfig,
    updateMembershipLevelImage,
    loading,
    error,
  };
};

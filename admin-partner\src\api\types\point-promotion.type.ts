import { TypeActive, TypeCondition, TypeDiscount } from "./voucher.type";

export interface PointPromotion {
  pointPromotionId?: string;
  partnerId?: string;
  shopId: string;
  name: string;
  discountType: TypeDiscount;
  exchangePoints?: number;
  minExchangePoints?: number;
  maxExchangePoints?: number;
  quantity: number;
  totalQuantity: number;
  quantityUsed?: number;
  groupName?: string;
  userIds?: string[];
  numUse: number;
  isFixed: boolean;
  isUsed: boolean;
  isLongTerm: boolean;
  startDate: Date | string;
  endDate?: Date | string | null;
  active: TypeActive;
  status?: string;
  created?: Date | string;
  updated?: Date | string;
}

export interface PointPromotionInput {
  shopId: string;
  name: string;
  exchangePoints?: number;
  minExchangePoints?: number;
  maxExchangePoints?: number;
  quantity: number;
  totalQuantity: number;
  discountType: TypeDiscount;
  groupName?: string;
  userIds?: string[];
  numUse: number;
  isFixed: boolean;
  isUsed: boolean;
  isLongTerm: boolean;
  startDate: Date | string;
  endDate?: Date | string | null;
  active: TypeActive;
}

export interface PointVoucherCode {
  pointVoucherCodeId: string;
  pointPromotionId: string;
  shopId: string;
  partnerId: string;
  PointVoucherCodeImage: string;
  pointsCode: string;
  rewardPoints: number;
  isUsed: boolean;
  usedCount: number;
  maxUsageCount: number;
  usedDate?: Date | string;
  expirationDate?: Date | string;
  status: string;
  created: Date | string;
  updated: Date | string;
}

# Hướng dẫn Authentication với Referral Code

## Tổng quan

Hệ thống authentication đã được tích hợp vào dự án **ecommerce-zalo-mini-app-practice** với các tính năng:

- ✅ Đăng nhập bằng Zalo
- ✅ Đăng nhập bằng số điện thoại + mật khẩu  
- ✅ Đăng ký tài khoản với referral code
- ✅ Xử lý referral code từ URL
- ✅ State management với Jotai
- ✅ Persistent storage
- ✅ Auto logout khi token hết hạn

## Cách sử dụng Referral Code

### 1. Từ URL Parameters

Khi người dùng truy cập ứng dụng qua link có chứa referral code:

```
https://your-app.com/?ref=ABC123
https://your-app.com/?referralCode=ABC123
https://your-app.com/?refCode=ABC123
```

<PERSON><PERSON> thống sẽ tự động:
- Lưu referral code vào storage
- Hiể<PERSON> thị thông báo cho người dùng
- Xóa parameter khỏi URL

### 2. Nhập thủ công

Người dùng có thể nhập referral code trực tiếp trong form đăng ký/đăng nhập.

### 3. Từ QR Code

QR Code được tạo tự động với referral code của user hiện tại.

## API Endpoints

### Authentication

```javascript
// Đăng nhập với Zalo
POST /api/user/authuser/activeaccount
{
  "provider": "Zalo",
  "zlAccessToken": "...",
  "zlPhoneToken": "...",
  "fullname": "...",
  "avatar": "...",
  "zaloId": "...",
  "zaloIdByOA": "...",
  "referralCode": "ABC123"
}

// Lấy thông tin user hiện tại
GET /api/user/me?shopId=0fa960fa-b082-46ac-858d-836cac08893f
Headers: {
  "Authorization": "Bearer <access_token>",
  "Accept-Language": "vi"
}

// Đăng nhập với số điện thoại
POST /api/user/authuser/login
{
  "provider": "Phone",
  "phoneNumber": "+***********",
  "password": "hashed_password"
}

// Đăng ký tài khoản
POST /api/user/authuser/register
{
  "provider": "Phone",
  "phoneNumber": "+***********",
  "password": "hashed_password",
  "referralCode": "ABC123",
  "fullname": "Tên người dùng"
}
```

## Cách sử dụng trong Component

### 1. Sử dụng useAuth Hook

```javascript
import { useAuth } from '../hooks/useAuth';

function MyComponent() {
  const { 
    user, 
    isAuthenticated, 
    isLoading,
    loginWithZalo,
    loginWithPhone,
    register,
    logout,
    referralCode,
    getUserReferralCode
  } = useAuth();

  const handleZaloLogin = async () => {
    const result = await loginWithZalo(referralCode);
    if (result.success) {
      // Đăng nhập thành công
    }
  };

  return (
    <div>
      {isAuthenticated ? (
        <div>Xin chào {user.fullname}</div>
      ) : (
        <button onClick={handleZaloLogin}>
          Đăng nhập với Zalo
        </button>
      )}
    </div>
  );
}
```

### 2. Sử dụng trực tiếp với Jotai

```javascript
import { useAtom } from 'jotai';
import { userAtom, referralCodeAtom } from '../store/authStore';

function MyComponent() {
  const [user] = useAtom(userAtom);
  const [referralCode] = useAtom(referralCodeAtom);

  return (
    <div>
      User: {user?.fullname}
      Referral Code: {referralCode}
    </div>
  );
}
```

## Cấu hình Environment

Cập nhật file `.env` trong thư mục root:

```env
# Zalo Mini App Config (đã có sẵn)
APP_ID=1386906096441490111
ZMP_TOKEN=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...

# API Configuration (thêm mới)
REACT_APP_API_URL=https://dev-admin.evotech.vn
REACT_APP_DEFAULT_SHOP_ID=0fa960fa-b082-46ac-858d-836cac08893f
REACT_APP_ENV=development
```

## Cài đặt Dependencies

```bash
# Cài đặt dependencies mới
npm install axios md5

# Hoặc với yarn
yarn add axios md5
```

## Flow đăng nhập với Referral Code

1. **User truy cập app qua link có referral code**
   ```
   https://app.com/?ref=ABC123
   ```

2. **ReferralHandler tự động lưu referral code**
   - Lưu vào localStorage
   - Cập nhật Jotai state
   - Hiển thị toast notification

3. **User chọn đăng nhập**
   - Đăng nhập với Zalo: Tự động gửi referral code
   - Đăng nhập với phone: Có thể nhập referral code

4. **Backend xử lý**
   - Tạo/cập nhật tài khoản
   - Liên kết với người giới thiệu
   - Trả về access token

5. **Frontend lưu thông tin**
   - Lưu tokens vào localStorage
   - Cập nhật user state
   - Redirect về trang chủ

## Lưu ý quan trọng

1. **Security**: Mật khẩu được hash bằng MD5 trước khi gửi lên server
2. **Token Management**: Access token tự động được thêm vào header của mọi API call
3. **Error Handling**: Tất cả lỗi đều được xử lý và hiển thị toast
4. **Persistence**: User state được lưu trong localStorage
5. **Auto Logout**: Tự động logout khi token hết hạn (401 error)

## Testing

Để test referral code:

1. Mở app với URL: `http://localhost:3000/?ref=TEST123`
2. Kiểm tra toast notification hiển thị
3. Vào trang đăng nhập, referral code sẽ được tự động điền
4. Đăng nhập và kiểm tra API call có chứa referral code

## Troubleshooting

### Lỗi thường gặp:

1. **"Request failed with status code 400" khi register**:
   - Kiểm tra format số điện thoại (phải có 10-11 số)
   - Mật khẩu phải có ít nhất 8 ký tự
   - **QUAN TRỌNG**: Register không hash mật khẩu, Login mới hash MD5

2. **"Network Error"**: Kiểm tra API_URL trong .env

3. **"Invalid referral code"**: Kiểm tra format referral code

4. **"Token expired"**: Hệ thống sẽ tự động logout

5. **"Phone permission denied"**: Zalo login vẫn hoạt động, chỉ không có số điện thoại

### Debug API:

Sử dụng ApiDebugger component trong development mode:
```javascript
// Hiển thị trong LoginPage khi REACT_APP_ENV=development
<ApiDebugger />
```

Hoặc test trực tiếp với Node.js:
```bash
cd ecommerce-zalo-mini-app-practice
npm install axios md5
node test-api.js
```

import { useState } from "react";
import { shopService } from "../../services/shop/shop.service";
import { ErrorHandlerService } from "../../services/error-handler.service";
import { shopSettingService } from "../../services/shop-setting/shop-setting.service";

export type AhamovePaymentMethod = "CASH" | "BALANCE" | "CASH_BY_RECIPIENT";
export interface IShopSettingCreate {
  miniAppId: string;
  secretKey: string;
  checkoutSecretKey: string;
  shopId: string;
  shopSettingId?: string;
  partnerId: string;
  transportSettings?: {
    ahamoveBike: {
      isEnabled: boolean;
      paymentMethod: AhamovePaymentMethod | null;
      phoneNumber: string | null;
    };
  };
}

// Giá trị mặc định của shopSetting
export const defaultShopSetting: IShopSettingCreate = {
  shopSettingId: "",
  shopId: "",
  partnerId: "",
  miniAppId: "",
  secretKey: "",
  checkoutSecretKey: "",
};

export const useShopSetting = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const getDetailShopSetting = async (shopId: string) => {
    try {
      if (shopId) {
        setLoading(true);
        setError(null);
        const response = await shopSettingService.getDetailShopSetting(shopId);
        return response;
      }
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: true,
        logError: true,
      });
      setError(errorResponse.detail);
      throw errorResponse;
    } finally {
      setLoading(false);
    }
  };

  const createAndUpdateShopSetting = async (data: Partial<IShopSettingCreate>) => {
    try {
      if (data?.shopId) {
        setLoading(true);
        setError(null);
        const response = await shopSettingService.createAndUpdateShopSetting(data);
        return response;
      }
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: true,
        logError: true,
      });
      setError(errorResponse.detail);
      throw errorResponse;
    } finally {
      setLoading(false);
    }
  };

  return { createAndUpdateShopSetting, getDetailShopSetting, loading, error };
};

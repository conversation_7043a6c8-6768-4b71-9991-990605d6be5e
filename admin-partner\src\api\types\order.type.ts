export type TypeOrderStatus = "Pending" | "Verified" | "Paid" | "Success" | "Failed" | "Refund";

export enum ListOrderStatus {
  All,
  Pending,
  WaitingForDelivery,
  Delivering,
  Success,
  Failed,
  Refund,
}
export type TypeTransportStatus =
  | "Created"
  | "Verified"
  | "WaitingForDelivery"
  | "Delivered"
  | "Transporting"
  | "Delivering"
  | "Success"
  | "Waiting"
  | "Refunding"
  | "Refunded"
  | "Cancel";

export type TypePayStatus = "NotPaid" | "Paid" | "Refund";

export type TypeTransportService = "LCOD" | "AHAMOVE" | "JTEXPRESS";

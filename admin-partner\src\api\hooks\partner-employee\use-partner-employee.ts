import { useState } from "react";
import { ErrorHandlerService } from "../../services/error-handler.service";
import {
  PartnerEmployeeBodyCreateApi,
  PartnerEmployeeBodyUpdateApi,
  PartnerEmployeeParamDto,
  partnerEmployeeService,
} from "../../services/partner-employee/partner-employee.service";

export const usePartnerEmployee = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const createPartnerEmployee = async (data: PartnerEmployeeBodyCreateApi) => {
    try {
      setLoading(true);
      setError(null);
      const response = await partnerEmployeeService.createPartnerEmployee(data);
      return response;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: true,
        logError: true,
      });
      setError(errorResponse.detail);
    } finally {
      setLoading(false);
    }
  };

  const getPartnerEmployeeById = async (employeeId: string) => {
    try {
      setLoading(true);
      setError(null);
      const response = await partnerEmployeeService.getPartnerEmployeeById(employeeId);
      return response;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: true,
        logError: true,
      });
      setError(errorResponse.detail);
    } finally {
      setLoading(false);
    }
  };

  const updatePartnerEmployee = async (data: PartnerEmployeeBodyUpdateApi) => {
    try {
      setLoading(true);
      setError(null);
      const response = await partnerEmployeeService.updatePartnerEmployee(data);
      return response;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: true,
        logError: true,
      });
      setError(errorResponse.detail);
    } finally {
      setLoading(false);
    }
  };

  const listPartnerEmployee = async (data: PartnerEmployeeParamDto) => {
    try {
      setLoading(true);
      setError(null);
      const response = await partnerEmployeeService.listPartnerEmployee(data);
      return response;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: true,
        logError: true,
      });
      setError(errorResponse.detail);
    } finally {
      setLoading(false);
    }
  };

  const deleteMultiPartnerEmployee = async (employeeIds: string[]) => {
    try {
      setLoading(true);
      setError(null);
      const response = await partnerEmployeeService.deleteMultiPartnerEmployee(employeeIds);
      return response;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: true,
        logError: true,
      });
      setError(errorResponse.detail);
    } finally {
      setLoading(false);
    }
  };

  const changePasswordEmployee = async (data: any) => {
    try {
      setLoading(true);
      setError(null);
      const response = await partnerEmployeeService.changePasswordEmployee(data);
      return response;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: true,
        logError: true,
      });
      setError(errorResponse.detail);
    } finally {
      setLoading(false);
    }
  };

  return {
    createPartnerEmployee,
    getPartnerEmployeeById,
    updatePartnerEmployee,
    listPartnerEmployee,
    deleteMultiPartnerEmployee,
    changePasswordEmployee,
    loading,
    error,
  };
};

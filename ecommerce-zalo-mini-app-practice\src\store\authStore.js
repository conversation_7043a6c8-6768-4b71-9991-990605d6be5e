import { atom } from 'jotai';
import { authAPI } from '../utils/api';
import { 
  getUserFromStorage, 
  setUserToStorage, 
  clearUserFromStorage,
  getReferralCode,
  setReferralCode 
} from '../utils/storage';

// Auth state atoms
export const userAtom = atom(getUserFromStorage());
export const isAuthenticatedAtom = atom(false);
export const isLoadingAtom = atom(false);
export const authErrorAtom = atom(null);
export const referralCodeAtom = atom(getReferralCode() || '');

// Derived atom to check authentication status
export const authStatusAtom = atom((get) => {
  const user = get(userAtom);
  const token = localStorage.getItem('accessToken');
  return !!(user && token);
});

// Auth actions
export const authActions = {
  // Initialize auth state on app start
  initializeAuth: () => {
    const user = getUserFromStorage();
    const token = localStorage.getItem('accessToken');
    
    if (user && token) {
      return { user, isAuthenticated: true };
    }
    
    return { user: null, isAuthenticated: false };
  },

  // Login with Zalo
  loginWithZalo: async (referralCode = '') => {
    try {
      const response = await authAPI.loginWithZalo(referralCode);
      
      if (response && response.accessToken) {
        setUserToStorage(response);
        return {
          success: true,
          user: response,
          message: 'Đăng nhập thành công!'
        };
      }
      
      return {
        success: false,
        error: 'Đăng nhập thất bại'
      };
    } catch (error) {
      console.error('Login with Zalo error:', error);
      return {
        success: false,
        error: error.response?.data?.message || 'Đăng nhập thất bại'
      };
    }
  },

  // Login with phone and password
  loginWithPhone: async (phoneNumber, password) => {
    try {
      const response = await authAPI.loginWithPhone(phoneNumber, password);
      
      if (response && response.accessToken) {
        setUserToStorage(response);
        return {
          success: true,
          user: response,
          message: 'Đăng nhập thành công!'
        };
      }
      
      return {
        success: false,
        error: 'Đăng nhập thất bại'
      };
    } catch (error) {
      console.error('Login with phone error:', error);
      return {
        success: false,
        error: error.response?.data?.message || 'Số điện thoại hoặc mật khẩu không đúng'
      };
    }
  },

  // Register new account
  register: async (phoneNumber, password, referralCode = '') => {
    try {
      const response = await authAPI.register(phoneNumber, password, referralCode);

      if (response && response.accessToken) {
        setUserToStorage(response);
        return {
          success: true,
          user: response,
          message: 'Đăng ký thành công!'
        };
      }

      return {
        success: false,
        error: 'Đăng ký thất bại'
      };
    } catch (error) {
      console.error('Register error:', error);

      // Extract detailed error message
      let errorMessage = 'Đăng ký thất bại';

      if (error.response?.data) {
        const errorData = error.response.data;
        if (typeof errorData === 'string') {
          errorMessage = errorData;
        } else if (errorData.message) {
          errorMessage = errorData.message;
        } else if (errorData.detail) {
          errorMessage = errorData.detail;
        } else if (errorData.error) {
          errorMessage = errorData.error;
        }
      } else if (error.message) {
        errorMessage = error.message;
      }

      return {
        success: false,
        error: errorMessage
      };
    }
  },

  // Logout
  logout: async () => {
    try {
      await authAPI.logout();
      clearUserFromStorage();
      return {
        success: true,
        message: 'Đăng xuất thành công!'
      };
    } catch (error) {
      console.error('Logout error:', error);
      // Still clear local storage even if API call fails
      clearUserFromStorage();
      return {
        success: true,
        message: 'Đăng xuất thành công!'
      };
    }
  },

  // Set referral code
  setReferralCode: (code) => {
    setReferralCode(code);
    return code;
  },

  // Get current user
  getCurrentUser: () => {
    return authAPI.getCurrentUser();
  },

  // Check if authenticated
  isAuthenticated: () => {
    return authAPI.isAuthenticated();
  },
};

// Atom for auth actions (to be used with useAtom)
export const authActionsAtom = atom(null, (get, set, action) => {
  switch (action.type) {
    case 'INITIALIZE':
      const initResult = authActions.initializeAuth();
      set(userAtom, initResult.user);
      set(isAuthenticatedAtom, initResult.isAuthenticated);
      set(isLoadingAtom, false);
      break;

    case 'LOGIN_START':
      set(isLoadingAtom, true);
      set(authErrorAtom, null);
      break;

    case 'LOGIN_SUCCESS':
      set(userAtom, action.payload.user);
      set(isAuthenticatedAtom, true);
      set(isLoadingAtom, false);
      set(authErrorAtom, null);
      break;

    case 'LOGIN_ERROR':
      set(userAtom, null);
      set(isAuthenticatedAtom, false);
      set(isLoadingAtom, false);
      set(authErrorAtom, action.payload.error);
      break;

    case 'LOGOUT':
      set(userAtom, null);
      set(isAuthenticatedAtom, false);
      set(isLoadingAtom, false);
      set(authErrorAtom, null);
      break;

    case 'SET_REFERRAL_CODE':
      set(referralCodeAtom, action.payload.code);
      authActions.setReferralCode(action.payload.code);
      break;

    default:
      break;
  }
});

export default {
  userAtom,
  isAuthenticatedAtom,
  isLoadingAtom,
  authErrorAtom,
  referralCodeAtom,
  authStatusAtom,
  authActionsAtom,
  authActions,
};

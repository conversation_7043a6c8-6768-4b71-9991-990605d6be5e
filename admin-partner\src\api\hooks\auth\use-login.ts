import { useEffect, useState } from "react";
import { authService } from "../../services/auth/auth.service";
import { ErrorHandlerService } from "../../services/error-handler.service";
import type { LoginProvider } from "../../types/auth.types";
import { StorageService, TokenService } from "nextjs-api-lib";
import { logger } from "@/src/utils/logger";
import { apiClient, updateAuthHeader } from "../../config/api-client";
import { requestNotificationPermission } from "@/src/config/firebase";

interface LoginParams {
  identifier: string;
  password: string;
  provider: LoginProvider;
}

export const useLogin = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [deviceToken, setDeviceToken] = useState<string | null>(null);

  // useEffect(() => {
  //   const initializeNotifications = async () => {
  //     const token = await requestNotificationPermission();
  //     console.log("token: ", token);
  //     if (token) {
  //       setDeviceToken(token);
  //     }
  //   };

  //   initializeNotifications();
  // }, []);

  const clearAllTokens = () => {
    try {
      localStorage.clear();
      sessionStorage.clear();
      StorageService.remove("partnerId");
      logger.info("Cleared all tokens and storage");
    } catch (err) {
      logger.error("Error clearing tokens:", err);
    }
  };

  const login = async ({ identifier, password, provider }: LoginParams) => {
    try {
      setLoading(true);
      setError(null);

      if (!password) {
        throw new Error("Password is required");
      }

      const loginData = {
        provider,
        password,
        phoneNumber: provider === "Phone" ? identifier : "",
        email: provider === "Email" ? identifier : "",
        username: "",
        code2FA: "",
      };

      const response = await authService.login<{
        accessToken?: string;
        refreshToken?: string;
        idFor?: string;
      }>(loginData, {
        showSnackbar: true,
        logError: true,
      });

      if (!response.data?.accessToken || !response.data?.refreshToken) {
        throw new Error("Invalid login response: Missing tokens");
      }

      // Đợi tất cả các thao tác với token hoàn tất
      if (response.data?.accessToken && response.data?.refreshToken) {
        await Promise.all([
          // Clear all existing tokens before login
          clearAllTokens(),
          TokenService.setAuthToken(response.data.accessToken, "partner"),
          TokenService.setRefreshToken(response.data.refreshToken, "partner"),
          // registerDevice(deviceToken),
          // Thêm delay nhỏ để đảm bảo token được set
          new Promise((resolve) => setTimeout(resolve, 100)),
        ]);

        // Update Authorization header sau khi set token
        updateAuthHeader(response.data.accessToken);

        if (response.data?.idFor) {
          StorageService.set("partnerId", response.data.idFor);
        }
      }

      return response;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: false,
        logError: true,
      });
      setError(errorResponse.detail);
      throw errorResponse;
    } finally {
      setLoading(false);
    }
  };

  const refreshToken = async (refreshToken: string) => {
    try {
      setLoading(true);
      setError(null);

      const response = await authService.refreshToken<{
        accessToken?: string;
        refreshToken?: string;
      }>(refreshToken, {
        showSnackbar: false,
        logError: true,
      });

      if (response.data?.accessToken) {
        TokenService.setAuthToken(response.data.accessToken, "partner");
      }
      if (response.data?.refreshToken) {
        TokenService.setRefreshToken(response.data.refreshToken, "partner");
      }

      return response;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: false,
        logError: true,
      });
      setError(errorResponse.detail);
      throw errorResponse;
    } finally {
      setLoading(false);
    }
  };

  return {
    login,
    refreshToken,
    loading,
    error,
    clearError: () => setError(null),
  } as const;
};

# name: CI for Next.js Application

# on:
#   push:
#     branches:
#       - staging
#   pull_request:
#     branches:
#       - staging

# jobs:
#   build-and-test:
#     name: Build and Test
#     runs-on: ubuntu-latest
#     strategy:
#       matrix:
#         node-version: [18.x]

#     steps:
#     - name: Checkout code
#       uses: actions/checkout@v3

#     - name: Set up Node.js ${{ matrix.node-version }}
#       uses: actions/setup-node@v3
#       with:
#         node-version: ${{ matrix.node-version }}
#         cache: 'npm'

    # - name: Install dependencies
    #   run: yarn install

    # - name: Run tests
    #   run: npm test

    # - name: Build Next.js app
    #   run: npm run build 
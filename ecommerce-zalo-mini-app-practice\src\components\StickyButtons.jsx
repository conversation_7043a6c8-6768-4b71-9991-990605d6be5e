import { Box } from 'zmp-ui';
import { useState } from 'react';
import { openChat, favoriteApp, minimizeApp, showToast } from 'zmp-sdk/apis';
import ShareModal from './ShareModal';
import favoriteIcon from '../static/favorite-sticky.svg'; // Icon ngôi sao outline - luôn dùng icon này
import messageIcon from '../static/message-text-sticky.svg';
import shareIcon from '../static/share-sticky.svg';
import minimizeIcon from '../static/arrows-diagonal-minimize-2.svg';

const StickyButtons = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [showShareModal, setShowShareModal] = useState(false);
  // Không track favorite state - luôn hiển thị icon "Add to Favorite"

  // Không cần useEffect phức tạp để track favorite state

  const toggleMenu = () => {
    setIsMenuOpen(!isMenuOpen);
  };

  const handleShareClick = () => {
    setShowShareModal(true);
  };

  const handleChatClick = async () => {
    try {
      const result = await openChat({
        type: "oa",
        id: "2958747652443832938",
        message: "Xin chào! Tôi quan tâm đến SonNH Store."
      });
      console.log('Chat result:', result);
    } catch (error) {
      console.error('Chat error:', error);
      showToast({
        message: 'Có lỗi xảy ra khi mở chat!',
        type: 'error',
        duration: 3000
      });
    }
  };

  const handleFavoriteClick = async () => {
    try {
      // Luôn gọi favoriteApp() - không toggle
      const result = await favoriteApp();
      console.log('Favorite result:', result);
    } catch (error) {
      console.error('Favorite error:', error);
      showToast({
        message: 'Có lỗi xảy ra khi thao tác yêu thích!',
        type: 'error',
        duration: 3000
      });
    }
  };

  const handleMinimizeClick = async () => {
    try {
      await minimizeApp();
      console.log('App minimized successfully');
    } catch (error) {
      console.error('Minimize error:', error);
      showToast({
        message: 'Có lỗi xảy ra khi thu nhỏ ứng dụng!',
        type: 'error',
        duration: 3000
      });
    }
  };

  const handleMenuItemClick = (action) => {
    console.log('Menu item clicked:', action);
    setIsMenuOpen(false); // Close menu after click

    if (action === 'share') {
      handleShareClick();
    } else if (action === 'message') {
      handleChatClick();
    } else if (action === 'favorite') {
      handleFavoriteClick();
    } else if (action === 'minimize') {
      handleMinimizeClick();
    }
  };

  // Tính toán vị trí nửa hình tròn bên trái cho từng button
  const getArcPosition = (index, total) => {
    const radius = 80; // Bán kính nửa hình tròn
    const startAngle = -90; // Bắt đầu từ dưới (-90 độ)
    const endAngle = 90; // Kết thúc ở trên (90 độ) - tạo nửa hình tròn
    const angleStep = (endAngle - startAngle) / (total - 1);
    const angle = startAngle + (index * angleStep);
    const radian = (angle * Math.PI) / 180;

    return {
      x: -Math.abs(radius * Math.cos(radian)), // Luôn âm để về phía bên trái
      y: radius * Math.sin(radian)
    };
  };

  const menuItems = [
    { icon: shareIcon, alt: 'Share', action: 'share' },
    { icon: messageIcon, alt: 'Message', action: 'message' },
    // {
    //   icon: favoriteIcon, // Luôn dùng icon outline
    //   alt: 'Thêm vào yêu thích',
    //   action: 'favorite'
    // },
    { icon: minimizeIcon, alt: 'Thu nhỏ app', action: 'minimize' }
  ];

  return (
    <Box className="sticky-buttons z-50 flex flex-col items-end">
      {/* Menu Items với hiệu ứng nửa hình tròn bên trái */}
      {menuItems.map((item, index) => {
        const position = getArcPosition(index, menuItems.length);
        return (
          <button
            key={item.action}
            onClick={() => handleMenuItemClick(item.action)}
            className={`absolute w-12 h-12 md:w-14 md:h-14 bg-white hover:bg-gray-50 rounded-full flex items-center justify-center shadow-lg border border-gray-200 transition-all duration-500 ease-out transform ${
              isMenuOpen
                ? 'opacity-100 scale-100 pointer-events-auto'
                : 'opacity-0 scale-75 pointer-events-none'
            }`}
            style={{
              transform: isMenuOpen
                ? `translate(${position.x}px, ${position.y}px) scale(1)`
                : 'translate(0px, 0px) scale(0.75)',
              transitionDelay: isMenuOpen ? `${index * 100}ms` : '0ms',
              right: 0, // Đặt relative từ button chính
              bottom: 0
            }}
          >
            <img src={item.icon} alt={item.alt} className="w-5 h-5 md:w-6 md:h-6 object-contain" />
          </button>
        );
      })}

      {/* Menu Toggle Button - giữ vị trí cũ */}
      <button
        onClick={toggleMenu}
        className={`w-12 h-12 md:w-14 md:h-14 bg-white hover:bg-gray-50 rounded-full flex items-center justify-center shadow-lg transition-all duration-500 transform hover:scale-105 border border-gray-200 z-10 ${
          isMenuOpen ? 'rotate-45' : 'rotate-0'
        }`}
      >
        {isMenuOpen ? (
          <svg className="w-6 h-6 md:w-7 md:h-7 text-red-500 transition-all duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
          </svg>
        ) : (
          <svg className="w-6 h-6 md:w-7 md:h-7 transition-all duration-300" fill="none" stroke="#1531AD" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
          </svg>
        )}
      </button>

      {/* Zalo Button - Commented out - không dùng nữa */}
      {/*
      <button className="relative w-12 h-12 md:w-14 md:h-14 bg-white hover:bg-gray-50 rounded-full flex items-center justify-center shadow-lg transition-all duration-200 transform hover:scale-105 border border-gray-200 z-10 mt-3">
        <div className="flex flex-col items-center">
          <img src={clickSymbol} alt="Click Symbol" className="w-4 h-4 md:w-5 md:h-5 object-contain mb-0.5" />
          <div className="text-xs md:text-xs leading-tight font-bold" style={{ color: '#1531AD' }}>Về Zalo</div>
        </div>
      </button>
      */}

      {/* Share Modal */}
      <ShareModal
        visible={showShareModal}
        onClose={() => setShowShareModal(false)}
      />
    </Box>
  );
};

export default StickyButtons;

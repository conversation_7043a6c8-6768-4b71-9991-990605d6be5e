import { useState } from "react";
import { useStoreId } from "@/src/hooks/use-store-id";
import { ErrorHandlerService } from "@/src/api/services/error-handler.service";
import { StorageService } from "nextjs-api-lib";
import { pointPromotionService } from "@/src/api/services/point-promotion/point-promotion.service";
import { PointPromotion, PointPromotionInput } from "@/src/api/types/point-promotion.type";
import { TypeActive } from "@/src/api/types/voucher.type";

export const usePointPromotion = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const partnerId = StorageService.get("partnerId") as string | null;

  const listPointPromotion = async (data: {
    skip: number;
    limit: number;
    shopId: string;
    search?: string;
    searchType?: string;
    statusPromotion?: string;
  }) => {
    try {
      setLoading(true);
      setError(null);

      const params = {
        skip: data.skip.toString(),
        limit: data.limit.toString(),
        shopId: data.shopId,
        search: data.search,
        searchType: data.searchType,
        statusPromotion: data.statusPromotion,
      };

      const response = await pointPromotionService.listPointPromotion(params);
      return response;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: true,
        logError: true,
      });
      setError(errorResponse.detail);
    } finally {
      setLoading(false);
    }
  };

  const createPointPromotion = async (data: PointPromotionInput) => {
    try {
      setLoading(true);
      setError(null);
      const response = await pointPromotionService.createPointPromotion(data);
      return response;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: true,
        logError: true,
      });
      setError(errorResponse.detail);
    } finally {
      setLoading(false);
    }
  };

  const updatePointPromotion = async (data: PointPromotion) => {
    try {
      setLoading(true);
      setError(null);
      const response = await pointPromotionService.updatePointPromotion(data);
      return response;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: true,
        logError: true,
      });
      setError(errorResponse.detail);
    } finally {
      setLoading(false);
    }
  };

  const detailPointPromotion = async (promotionId: string) => {
    try {
      setLoading(true);
      setError(null);
      const response = await pointPromotionService.detailPointPromotion(promotionId);
      return response;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: true,
        logError: true,
      });
      setError(errorResponse.detail);
    } finally {
      setLoading(false);
    }
  };

  const deletePointPromotion = async (promotionIds: string[]) => {
    try {
      setLoading(true);
      setError(null);
      const response = await pointPromotionService.deletePointPromotion(promotionIds);
      return response;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: true,
        logError: true,
      });
      setError(errorResponse.detail);
    } finally {
      setLoading(false);
    }
  };

  const changeActivePointPromotion = async (data: {
    promotionIds: string[];
    active: TypeActive;
  }) => {
    try {
      setLoading(true);
      setError(null);
      const response = await pointPromotionService.changeActivePointPromotion(
        data.promotionIds,
        data.active
      );
      return response;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: true,
        logError: true,
      });
      setError(errorResponse.detail);
    } finally {
      setLoading(false);
    }
  };

  const listPointVoucherCodes = async (
    promotionId: string,
    skip: number = 0,
    limit: number = 20,
    isUsed?: boolean,
    isExpired?: boolean,
    pointsCode?: string
  ) => {
    try {
      setLoading(true);
      setError(null);
      const response = await pointPromotionService.listPointVoucherCodes(
        promotionId,
        skip,
        limit,
        isUsed,
        isExpired,
        pointsCode
      );
      return response;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: true,
        logError: true,
      });
      setError(errorResponse.detail);
    } finally {
      setLoading(false);
    }
  };

  return {
    loading,
    error,
    listPointPromotion,
    createPointPromotion,
    updatePointPromotion,
    detailPointPromotion,
    deletePointPromotion,
    changeActivePointPromotion,
    listPointVoucherCodes,
  };
};

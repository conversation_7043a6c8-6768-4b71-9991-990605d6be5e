import { useState } from "react";
import { useStoreId } from "@/src/hooks/use-store-id";
import { GetProductCategoryRequest, CategoryType } from "@/src/api/types/product-category.types";
import { productCategoryService } from "@/src/api/services/dashboard/product/category.service";
import { ErrorHandlerService } from "@/src/api/services/error-handler.service";
import { StorageService } from "nextjs-api-lib";
import { voucherService } from "@/src/api/services/voucher/voucher.service";

export const useVoucher = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const partnerId = StorageService.get("partnerId") as string | null;
  const getProductCategory = async (
    skip: number,
    limit: number,
    categoryType: CategoryType = "Product",
    search: string = "",
    shopId: string
  ) => {
    try {
      setLoading(true);
      setError(null);
      const params = {
        shopId,
        partnerId: partnerId || null,
        categoryType,
        search,
        skip,
        limit,
      };
      const response = await voucherService.getProductCategory(params);
      return response;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: true,
        logError: true,
      });
      setError(errorResponse.detail);
    } finally {
      setLoading(false);
    }
  };

  const createVoucherPromotion = async (data: any) => {
    try {
      setLoading(true);
      setError(null);
      const response = await voucherService.createVoucherPromotion({
        ...data,
        partnerId,
      });
      return response;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: true,
        logError: true,
      });
      setError(errorResponse.detail);
    } finally {
      setLoading(false);
    }
  };

  const getDetailVoucher = async (data: any) => {
    try {
      setLoading(true);
      setError(null);
      const response = await voucherService.getDetailVoucher(data);
      return response;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: true,
        logError: true,
      });
      setError(errorResponse.detail);
    } finally {
      setLoading(false);
    }
  };

  const listVoucher = async (data) => {
    try {
      setLoading(true);
      setError(null);

      const params = {
        shopId: data.shopId,
        skip: data.skip,
        limit: data.limit,
        searchType: data.searchType,
        search: data.search,
        statusVoucher: data.statusVoucher,
        type: data.type,
      };

      const response = await voucherService.listVoucher(params);
      return response;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: true,
        logError: true,
      });
      setError(errorResponse.detail);
    } finally {
      setLoading(false);
    }
  };

  const deleteVoucher = async (voucherIds: string[]) => {
    try {
      setLoading(true);
      setError(null);
      const response = await voucherService.deleteVoucher(voucherIds);
      return response;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: true,
        logError: true,
      });
      setError(errorResponse.detail);
    } finally {
      setLoading(false);
    }
  };

  const changeActiveVoucher = async (data: any) => {
    try {
      setLoading(true);
      setError(null);
      const response = await voucherService.changeActiveVoucher(data);
      return response;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: true,
        logError: true,
      });
      setError(errorResponse.detail);
    } finally {
      setLoading(false);
    }
  };

  const updateVoucherPromotion = async (data: any) => {
    try {
      setLoading(true);
      setError(null);
      const response = await voucherService.updateVoucherPromotion({
        ...data,
        partnerId,
      });
      return response;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: true,
        logError: true,
      });
      setError(errorResponse.detail);
    } finally {
      setLoading(false);
    }
  };

  const listItemsByItemsIds = async (data: any) => {
    try {
      setLoading(true);
      setError(null);
      const response = await voucherService.listItemsByItemsIds(data);
      return response;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: true,
        logError: true,
      });
      setError(errorResponse.detail);
    } finally {
      setLoading(false);
    }
  };

  const createVoucherTransport = async (data: any) => {
    try {
      setLoading(true);
      setError(null);
      const response = await voucherService.createVoucherTransport({
        ...data,
        partnerId,
      });
      return response;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: true,
        logError: true,
      });
      setError(errorResponse.detail);
    } finally {
      setLoading(false);
    }
  };

  const updateVoucherTransport = async (data: any) => {
    try {
      setLoading(true);
      setError(null);
      const response = await voucherService.updateVoucherTransport({
        ...data,
        partnerId,
      });
      return response;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: true,
        logError: true,
      });
      setError(errorResponse.detail);
    } finally {
      setLoading(false);
    }
  };
  const searchVoucher = async (data: any) => {
    try {
      setLoading(true);
      setError(null);
      const response = await voucherService.searchVoucher(data);
      return response;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: true,
        logError: true,
      });
      setError(errorResponse.detail);
    } finally {
      setLoading(false);
    }
  };

  const validateVoucher = async (data: any) => {
    try {
      setLoading(true);
      setError(null);
      const response = await voucherService.validateVoucher(data);
      return response;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: true,
        logError: true,
      });
      setError(errorResponse.detail);
    } finally {
      setLoading(false);
    }
  };
  return {
    getProductCategory,
    createVoucherPromotion,
    getDetailVoucher,
    listVoucher,
    deleteVoucher,
    changeActiveVoucher,
    updateVoucherPromotion,
    listItemsByItemsIds,
    createVoucherTransport,
    updateVoucherTransport,
    searchVoucher,
    validateVoucher,
    loading,
    error,
  };
};

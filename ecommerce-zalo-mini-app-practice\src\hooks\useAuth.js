import { use<PERSON>tom } from 'jotai';
import { 
  user<PERSON>tom, 
  isAuthenticated<PERSON>tom, 
  isLoading<PERSON>tom,
  authErrorAtom,
  referralCodeAtom,
  authActionsAtom,
  authStatusAtom 
} from '../store/authStore';
import { authActions } from '../store/authStore';

// Custom hook for authentication
export const useAuth = () => {
  const [user] = useAtom(userAtom);
  const [isAuthenticated] = useAtom(authStatusAtom);
  const [isLoading] = useAtom(isLoadingAtom);
  const [authError] = useAtom(authErrorAtom);
  const [referralCode] = useAtom(referralCodeAtom);
  const [, dispatch] = useAtom(authActionsAtom);

  // Login with Zalo
  const loginWithZalo = async (refCode = '') => {
    dispatch({ type: 'LOGIN_START' });

    try {
      const result = await authActions.loginWithZalo(refCode);
      
      if (result.success) {
        dispatch({ 
          type: 'LOGIN_SUCCESS', 
          payload: { user: result.user } 
        });
        return result;
      } else {
        dispatch({ 
          type: 'LOGIN_ERROR', 
          payload: { error: result.error } 
        });
        return result;
      }
    } catch (error) {
      const errorMessage = 'Đăng nhập thất bại';
      dispatch({ 
        type: 'LOGIN_ERROR', 
        payload: { error: errorMessage } 
      });
      return { success: false, error: errorMessage };
    }
  };

  // Login with phone
  const loginWithPhone = async (phoneNumber, password) => {
    dispatch({ type: 'LOGIN_START' });

    try {
      const result = await authActions.loginWithPhone(phoneNumber, password);
      
      if (result.success) {
        dispatch({ 
          type: 'LOGIN_SUCCESS', 
          payload: { user: result.user } 
        });
        return result;
      } else {
        dispatch({ 
          type: 'LOGIN_ERROR', 
          payload: { error: result.error } 
        });
        return result;
      }
    } catch (error) {
      const errorMessage = 'Đăng nhập thất bại';
      dispatch({ 
        type: 'LOGIN_ERROR', 
        payload: { error: errorMessage } 
      });
      return { success: false, error: errorMessage };
    }
  };

  // Register
  const register = async (phoneNumber, password, refCode = '') => {
    dispatch({ type: 'LOGIN_START' });

    try {
      const result = await authActions.register(phoneNumber, password, refCode);
      
      if (result.success) {
        dispatch({ 
          type: 'LOGIN_SUCCESS', 
          payload: { user: result.user } 
        });
        return result;
      } else {
        dispatch({ 
          type: 'LOGIN_ERROR', 
          payload: { error: result.error } 
        });
        return result;
      }
    } catch (error) {
      const errorMessage = 'Đăng ký thất bại';
      dispatch({ 
        type: 'LOGIN_ERROR', 
        payload: { error: errorMessage } 
      });
      return { success: false, error: errorMessage };
    }
  };

  // Logout
  const logout = async () => {
    try {
      await authActions.logout();
      dispatch({ type: 'LOGOUT' });
      return { success: true, message: 'Đăng xuất thành công!' };
    } catch (error) {
      dispatch({ type: 'LOGOUT' });
      return { success: true, message: 'Đăng xuất thành công!' };
    }
  };

  // Set referral code
  const setReferralCode = (code) => {
    dispatch({ 
      type: 'SET_REFERRAL_CODE', 
      payload: { code } 
    });
  };

  // Check if user has specific role or permission
  const hasRole = (role) => {
    return user?.roles?.includes(role) || false;
  };

  // Check if user is premium member
  const isPremiumMember = () => {
    return user?.membershipLevel === 'premium' || user?.membershipLevel === 'gold';
  };

  // Get user's referral code
  const getUserReferralCode = () => {
    return user?.referralCode || user?.userCode || '';
  };

  return {
    // State
    user,
    isAuthenticated,
    isLoading,
    authError,
    referralCode,

    // Actions
    loginWithZalo,
    loginWithPhone,
    register,
    logout,
    setReferralCode,

    // Utilities
    hasRole,
    isPremiumMember,
    getUserReferralCode,
  };
};

export default useAuth;

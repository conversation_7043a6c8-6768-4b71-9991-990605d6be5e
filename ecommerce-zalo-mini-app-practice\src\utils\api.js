import axios from 'axios';
import md5 from 'md5';
import { getAccessToken, getUserInfo, getPhoneNumber } from 'zmp-sdk/apis';

// API Configuration
const API_BASE_URL = process.env.REACT_APP_API_URL || 'https://dev-admin.evotech.vn';

// Create axios instance
export const apiClient = axios.create({
  baseURL: API_BASE_URL,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
    'Accept-Language': 'vi',
  },
});

// Request interceptor to add auth token
apiClient.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('accessToken');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor for error handling
apiClient.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      // Clear tokens and redirect to login
      localStorage.removeItem('accessToken');
      localStorage.removeItem('refreshToken');
      localStorage.removeItem('user');
    }
    return Promise.reject(error);
  }
);

// Auth API functions
export const authAPI = {
  // Get current user info
  getCurrentUserInfo: async (shopId = null) => {
    try {
      const url = shopId ? `/api/user/me?shopId=${shopId}` : '/api/user/me';
      const response = await apiClient.get(url);
      return response.data;
    } catch (error) {
      console.error('Get user info error:', error);
      throw error;
    }
  },

  // Zalo authentication with referral code
  loginWithZalo: async (referralCode = '') => {
    try {
      // Get Zalo access token
      const accessToken = await getAccessToken();

      // Get user info from Zalo
      const { userInfo } = await getUserInfo({ autoRequestPermission: true });

      // Get phone number (optional)
      let phoneToken = null;
      try {
        const phoneResult = await getPhoneNumber({});
        phoneToken = phoneResult.token;
      } catch (phoneError) {
        console.log('Phone permission not granted:', phoneError);
      }

      // Call backend API to authenticate/register user
      const response = await apiClient.post('/api/user/authuser/activeaccount', {
        provider: 'Zalo',
        phoneNumber: '',
        zlPhoneToken: phoneToken || '',
        zlAccessToken: accessToken,
        fullname: userInfo.name,
        avatar: userInfo.avatar,
        zaloId: userInfo.id,
        zaloIdByOA: userInfo.idByOA,
        password: '',
        referralCode: referralCode,
      });

      // Store tokens and user info
      if (response.data) {
        localStorage.setItem('accessToken', response.data.accessToken);
        localStorage.setItem('refreshToken', response.data.refreshToken);
        localStorage.setItem('user', JSON.stringify(response.data));

        // Update axios default headers
        apiClient.defaults.headers.common['Authorization'] = `Bearer ${response.data.accessToken}`;
      }

      return response.data;
    } catch (error) {
      console.error('Zalo login error:', error);
      throw error;
    }
  },

  // Phone + Password login
  loginWithPhone: async (phoneNumber, password) => {
    try {
      // Format phone number
      const formattedPhone = phoneNumber.startsWith('+84')
        ? phoneNumber
        : phoneNumber.startsWith('0')
          ? `+84${phoneNumber.slice(1)}`
          : `+84${phoneNumber}`;

      const response = await apiClient.post('/api/user/authuser/login', {
        provider: 'Phone',
        phoneNumber: formattedPhone,
        password: md5(password), // MD5 hash password
      });

      if (response.data) {
        localStorage.setItem('accessToken', response.data.accessToken);
        localStorage.setItem('refreshToken', response.data.refreshToken);
        localStorage.setItem('user', JSON.stringify(response.data));
        
        apiClient.defaults.headers.common['Authorization'] = `Bearer ${response.data.accessToken}`;
      }

      return response.data;
    } catch (error) {
      console.error('Phone login error:', error);
      throw error;
    }
  },

  // Register with referral code
  register: async (phoneNumber, password, referralCode = '') => {
    try {
      // Format phone number
      const formattedPhone = phoneNumber.startsWith('+84')
        ? phoneNumber
        : phoneNumber.startsWith('0')
          ? `+84${phoneNumber.slice(1)}`
          : `+84${phoneNumber}`;

      const requestData = {
        provider: 'Phone',
        phoneNumber: formattedPhone,
        password: password, // DON'T hash password for register - only for login
        referralCode: referralCode || '',
        fullname: formattedPhone, // Use phone as fullname for now
      };

      console.log('Register request data:', requestData);

      const response = await apiClient.post('/api/user/authuser/register', requestData);

      if (response.data) {
        localStorage.setItem('accessToken', response.data.accessToken);
        localStorage.setItem('refreshToken', response.data.refreshToken);
        localStorage.setItem('user', JSON.stringify(response.data));
        
        apiClient.defaults.headers.common['Authorization'] = `Bearer ${response.data.accessToken}`;
      }

      return response.data;
    } catch (error) {
      console.error('Register error:', error);
      console.error('Error response:', error.response?.data);
      console.error('Error status:', error.response?.status);
      console.error('Error headers:', error.response?.headers);
      throw error;
    }
  },

  // Logout
  logout: async () => {
    try {
      const refreshToken = localStorage.getItem('refreshToken');
      if (refreshToken) {
        await apiClient.post('/api/user/authuser/logout', {
          refreshToken: refreshToken,
        });
      }
    } catch (error) {
      console.error('Logout error:', error);
    } finally {
      // Clear local storage
      localStorage.removeItem('accessToken');
      localStorage.removeItem('refreshToken');
      localStorage.removeItem('user');
      delete apiClient.defaults.headers.common['Authorization'];
    }
  },

  // Get current user
  getCurrentUser: () => {
    const userStr = localStorage.getItem('user');
    return userStr ? JSON.parse(userStr) : null;
  },

  // Check if user is authenticated
  isAuthenticated: () => {
    return !!localStorage.getItem('accessToken');
  },
};

export default apiClient;

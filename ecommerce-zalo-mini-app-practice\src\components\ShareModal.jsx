import { <PERSON>, Modal, Button } from 'zmp-ui';
import { useState, useEffect } from 'react';
import { openShareSheet, showToast, getUserInfo } from 'zmp-sdk/apis';

// Import icons
import shareIcon from '../static/share-sticky.svg';
import copyIcon from '../static/saochep.svg';

const ShareModal = ({ visible, onClose }) => {
  const [userInfo, setUserInfo] = useState(null);
  const [isLoggedIn, setIsLoggedIn] = useState(false);
  const [referralCode, setReferralCode] = useState('');

  useEffect(() => {
    if (visible) {
      checkUserLoginStatus();
    }
  }, [visible]);

  const checkUserLoginStatus = async () => {
    try {
      const user = await getUserInfo({});
      console.log('User info:', user);
      
      if (user && user.userInfo) {
        setUserInfo(user.userInfo);
        setIsLoggedIn(true);
        // Generate referral code based on user ID or use a mock code
        const mockReferralCode = generateReferralCode(user.userInfo.id || 'default');
        setReferralCode(mockReferralCode);
      } else {
        setIsLoggedIn(false);
        setReferralCode('');
      }
    } catch (error) {
      console.error('Error getting user info:', error);
      setIsLoggedIn(false);
      setReferralCode('');
    }
  };

  const generateReferralCode = (userId) => {
    // Generate a mock referral code - in real app this would come from backend
    const baseCode = userId.toString().slice(-4) || '0000';
    return `0981865${baseCode}`;
  };

  const handleCopyReferralCode = async () => {
    try {
      if (navigator.clipboard) {
        await navigator.clipboard.writeText(referralCode);
        showToast({
          message: 'Đã sao chép mã giới thiệu!',
          type: 'success',
          duration: 2000
        });
      } else {
        // Fallback for older browsers
        const textArea = document.createElement('textarea');
        textArea.value = referralCode;
        document.body.appendChild(textArea);
        textArea.select();
        document.execCommand('copy');
        document.body.removeChild(textArea);
        
        showToast({
          message: 'Đã sao chép mã giới thiệu!',
          type: 'success',
          duration: 2000
        });
      }
    } catch (error) {
      console.error('Copy error:', error);
      showToast({
        message: 'Không thể sao chép. Vui lòng thử lại.',
        type: 'error',
        duration: 2000
      });
    }
  };

  const handleShareNow = async () => {
    try {
      const shareContent = {
        title: 'SonNH Store - Ứng dụng mua sắm tuyệt vời!',
        description: isLoggedIn 
          ? `Hãy chia sẻ link này cho bạn bè của bạn để nhận hoa hồng trọn đời bất cứ khi nào bạn bè mua hàng tại NTT. Mã giới thiệu: ${referralCode}`
          : 'Hãy chia sẻ link này cho bạn bè của bạn để nhận hoa hồng trọn đời bất cứ khi nào bạn bè mua hàng tại NTT',
        link: isLoggedIn 
          ? `https://zalo.me/oa/2958747652443832938?ref=${referralCode}`
          : 'https://zalo.me/oa/2958747652443832938'
      };

      await openShareSheet(shareContent);
      onClose();
    } catch (error) {
      console.error('Share error:', error);
      showToast({
        message: 'Có lỗi xảy ra khi chia sẻ!',
        type: 'error',
        duration: 3000
      });
    }
  };

  const handleCopyLink = async () => {
    try {
      const shareLink = isLoggedIn 
        ? `https://zalo.me/oa/2958747652443832938?ref=${referralCode}`
        : 'https://zalo.me/oa/2958747652443832938';

      if (navigator.clipboard) {
        await navigator.clipboard.writeText(shareLink);
        showToast({
          message: 'Đã sao chép link!',
          type: 'success',
          duration: 2000
        });
      } else {
        // Fallback for older browsers
        const textArea = document.createElement('textarea');
        textArea.value = shareLink;
        document.body.appendChild(textArea);
        textArea.select();
        document.execCommand('copy');
        document.body.removeChild(textArea);
        
        showToast({
          message: 'Đã sao chép link!',
          type: 'success',
          duration: 2000
        });
      }
    } catch (error) {
      console.error('Copy link error:', error);
      showToast({
        message: 'Không thể sao chép link. Vui lòng thử lại.',
        type: 'error',
        duration: 2000
      });
    }
  };

  return (
    <Modal
      visible={visible}
      onClose={onClose}
      verticalActions
      title=""
      className="share-modal"
    >
      <Box className="p-0">
        {/* Header Image */}
        <Box className="relative bg-gradient-to-br from-purple-100 to-purple-200 p-6 rounded-t-lg">
          {/* Decorative elements */}
          <Box className="absolute top-4 right-4 w-8 h-8 bg-purple-300 rounded-full opacity-50"></Box>
          <Box className="absolute bottom-6 left-4 w-4 h-4 bg-purple-400 rounded-full opacity-30"></Box>
          
          {/* Main illustration */}
          <Box className="flex justify-center items-center h-32">
            <Box className="relative">
              {/* Money bag illustration */}
              <Box className="w-20 h-20 bg-gradient-to-b from-purple-800 to-purple-900 rounded-full relative">
                <Box className="absolute top-2 left-1/2 transform -translate-x-1/2 w-6 h-4 bg-purple-700 rounded-t-full"></Box>
                <Box className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 text-yellow-400 text-2xl font-bold">$</Box>
              </Box>
              {/* Hand illustration */}
              <Box className="absolute -left-8 top-4 w-12 h-8 bg-gradient-to-r from-orange-300 to-orange-400 rounded-lg transform -rotate-12"></Box>
            </Box>
          </Box>
        </Box>

        {/* Referral Code Section - Only show if logged in */}
        {isLoggedIn && (
          <Box className="mx-4 -mt-4 mb-4 relative z-10">
            <Box className="bg-brand-blue text-white p-4 rounded-lg flex items-center justify-between">
              <Box>
                <p className="text-sm font-medium">Mã giới thiệu của bạn</p>
              </Box>
              <Box className="flex items-center">
                <span className="text-xl font-bold mr-3">{referralCode}</span>
                <button 
                  onClick={handleCopyReferralCode}
                  className="p-2 bg-white bg-opacity-20 rounded-lg hover:bg-opacity-30 transition-all"
                >
                  <img src={copyIcon} alt="Copy" className="w-4 h-4" />
                </button>
              </Box>
            </Box>
          </Box>
        )}

        {/* Content */}
        <Box className="px-4 pb-4">
          <h3 className="text-lg font-bold text-center mb-2">Chia sẻ ngay</h3>
          <p className="text-sm text-gray-600 text-center mb-6 leading-relaxed">
            Hãy chia sẻ link này cho bạn bè của bạn để nhận<br />
            hoa hồng trọn đời bất cứ khi nào bạn bè mua<br />
            hàng tại NTT
          </p>

          {/* Action Buttons */}
          <Box className="space-y-3">
            {/* Copy Link Button */}
            <button 
              onClick={handleCopyLink}
              className="w-full flex items-center justify-between p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-all"
            >
              <Box className="flex items-center">
                <Box className="w-8 h-8 bg-brand-blue rounded-full flex items-center justify-center mr-3">
                  <img src={copyIcon} alt="Copy" className="w-4 h-4 filter brightness-0 invert" />
                </Box>
                <span className="font-medium text-gray-800">Sao chép link</span>
              </Box>
              <svg className="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
              </svg>
            </button>

            {/* Share Now Button */}
            <button 
              onClick={handleShareNow}
              className="w-full flex items-center justify-center p-4 bg-brand-blue text-white rounded-lg hover:bg-blue-700 transition-all font-medium"
            >
              <img src={shareIcon} alt="Share" className="w-5 h-5 mr-2 filter brightness-0 invert" />
              Chia sẻ ngay
            </button>
          </Box>
        </Box>
      </Box>
    </Modal>
  );
};

export default ShareModal;

import { Search } from "@mui/icons-material";
import { apiClient } from "@/src/api/config/api-client";
import { API_PATHS } from "@/src/api/constants/api-paths";
import type { ApiError, ErrorConfig } from "@/src/api/types/error.types";
import type { ExtendedRequestConfig } from "@/src/api/types/api.types";
import { GetProductRequest, GetProductRequestBody } from "@/src/api/types/product.types";
import {
  GetAffiliationPartnerRequest,
  GetCommissionOrderRequest,
  UpdateAffiliationPartnerRequest,
} from "../../types/affiliation.type";

function handleApiError(error: any): never {
  const status = error.response?.status || 500;
  const errorData = error.response?.data as ApiError;
  const errorMessage = errorData?.detail || errorData?.title || error.message;

  if (error.response) {
    throw {
      ...error,
      message: errorMessage,
    };
  }
  throw error;
}

export const affiliationService = {
  getReportOverview: async <T = any>(data: any, errorConfig?: ErrorConfig) => {
    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
    };

    const queryParams = new URLSearchParams({
      shopId: data.shopId,

      ...(data.fromDate && { fromDate: data.fromDate }),
      ...(data.toDate && { toDate: data.toDate }),
    });

    try {
      if (data.shopId) {
        const response = await apiClient.get<any, T>(
          `${API_PATHS.AFFILIATION.REPORT_OVERVIEW}?${queryParams.toString()}`,
          config
        );
        return response;
      }
    } catch (error: any) {
      handleApiError(error);
    }
  },

  getDashboardStatistic: async <T = any>(data: any, errorConfig?: ErrorConfig) => {
    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
    };

    const queryParams = new URLSearchParams({
      shopId: data.shopId,

      ...(data.fromDate && { fromDate: data.fromDate }),
      ...(data.toDate && { toDate: data.toDate }),
    });

    try {
      const response = await apiClient.get<any, T>(
        `${API_PATHS.AFFILIATION.DASHBOARD_STATISTIC}?${queryParams.toString()}`,
        config
      );
      return response;
    } catch (error: any) {
      handleApiError(error);
    }
  },

  getCommissionsConfig: async <T = any>(data: any, errorConfig?: ErrorConfig) => {
    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
    };

    const queryParams = new URLSearchParams({
      shopId: data.shopId,
    });

    try {
      const response = await apiClient.get<any, T>(
        `${API_PATHS.AFFILIATION.GET_COMMISSIONS_CONFIG}?${queryParams.toString()}`,
        config
      );
      return response;
    } catch (error: any) {
      handleApiError(error);
    }
  },

  updateCommissionsConfig: async <T = any>(params: any, data: any, errorConfig?: ErrorConfig) => {
    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
    };

    const queryParams = new URLSearchParams({
      shopId: params.shopId,
    });

    try {
      const response = await apiClient.put<any, T>(
        `${API_PATHS.AFFILIATION.UPDATE_COMMISSIONS_CONFIG}?${queryParams.toString()}`,
        data,
        config
      );
      return response;
    } catch (error: any) {
      handleApiError(error);
    }
  },

  updateLevelTwoStatus: async <T = any>(params: any, errorConfig?: ErrorConfig) => {
    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
    };

    const queryParams = new URLSearchParams({
      shopId: params.shopId,
      isActive: params.isActive,
    });

    try {
      const response = await apiClient.patch<any, T>(
        `${API_PATHS.AFFILIATION.UPDATE_LEVEL_TWO_STATUS}?${queryParams.toString()}`,
        config
      );
      return response;
    } catch (error: any) {
      handleApiError(error);
    }
  },

  updateCommissionActiveStatus: async <T = any>(params: any, errorConfig?: ErrorConfig) => {
    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
    };

    const queryParams = new URLSearchParams({
      shopId: params.shopId,
      isActive: params.isActive,
    });

    try {
      const response = await apiClient.put<any, T>(
        `${API_PATHS.AFFILIATION.UPDATE_ACTIVE_STATUS}?${queryParams.toString()}`,
        config
      );
      return response;
    } catch (error: any) {
      handleApiError(error);
    }
  },

  getAffiliationPartner: async <T = any>(
    data: GetAffiliationPartnerRequest,
    errorConfig?: ErrorConfig
  ) => {
    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
    };

    const queryParams = new URLSearchParams();

    if (data.ShopId) queryParams.append("ShopId", data.ShopId);
    if (data.PageIndex !== undefined) queryParams.append("PageIndex", data.PageIndex.toString());
    if (data.PageSize !== undefined) queryParams.append("PageSize", data.PageSize.toString());
    if (data.FromDate) queryParams.append("FromDate", data.FromDate);
    if (data.ToDate) queryParams.append("ToDate", data.ToDate);
    if (data.AffiliationStatus !== undefined)
      queryParams.append("AffiliationStatus", data.AffiliationStatus.toString());
    if (data.Search) queryParams.append("Search", data.Search);
    if (data.Search && data.Search.trim() !== "") {
      queryParams.set("Search", data.Search);
    }

    try {
      const response = await apiClient.get<any, T>(
        `${API_PATHS.AFFILIATION.GET_LIST_AFFILIATION_PARTNER}?${queryParams.toString()}`,
        config
      );
      return response;
    } catch (error: any) {
      handleApiError(error);
    }
  },

  exportExcelAffiliationPartner: async <T = any>(data: any, errorConfig?: ErrorConfig) => {
    const config: any = {
      errorHandling: errorConfig,
      responseType: "blob",
    };
    const queryParams = new URLSearchParams({
      ShopId: data.ShopId,
      AffiliationStatus: data.AffiliationStatus,
      FromDate: data.FromDate ? data.FromDate : "",
      ToDate: data.ToDate ? data.ToDate : "",
      SortType: "desc",
      NameType: "Created",
      PageSize: data.PageSize,
      PageIndex: data.PageIndex,
      Search: data.SearchTerm ? data.SearchTerm : "",
      Sort: "desc",
      Name: "Created",
    });

    try {
      const response = await apiClient.get<any, T>(
        `${API_PATHS.AFFILIATION.EXPORT_EXCEL_LIST_AFFILIATION_PARTNER}?${queryParams.toString()}`,
        config
      );
      return response;
    } catch (error: any) {
      handleApiError(error);
    }
  },

  updateAffiliationPartner: async <T = any>(
    userId: string,
    data: UpdateAffiliationPartnerRequest,
    errorConfig?: ErrorConfig
  ) => {
    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
    };

    try {
      if (userId) {
        const response = await apiClient.put<any, T>(
          `${API_PATHS.AFFILIATION.UPDATE_AFFILIATION_PARTNER}?id=${userId}`,
          data,
          config
        );
        return response;
      }
    } catch (error: any) {
      handleApiError(error);
    }
  },

  approvalAffiliationPartner: async <T = any>(userId: string, errorConfig?: ErrorConfig) => {
    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
    };

    try {
      if (userId) {
        const response = await apiClient.put<any, T>(
          `${API_PATHS.AFFILIATION.APPROVE_AFFILIATION_PARTNER}?id=${userId}`,
          config
        );
        return response;
      }
    } catch (error: any) {
      // handleApiError(error);
      return error;
    }
  },
  getQuickReportAffiliationPartner: async <T = any>(userId: string, errorConfig?: ErrorConfig) => {
    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
    };

    try {
      if (userId) {
        const response = await apiClient.get<any, T>(
          `${API_PATHS.AFFILIATION.GET_QUICK_REPORT_AFFILIATION_PARTNER.replace(":id", userId)}`,
          config
        );
        return response;
      }
    } catch (error: any) {
      // handleApiError(error);
      return error;
    }
  },

  getCommissionOrder: async <T = any>(data, errorConfig?: ErrorConfig) => {
    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
    };

    const queryParams = new URLSearchParams({
      shopId: data.shopId,
      PageNumber: data.PageNumber,
      PageSize: data.PageSize,
      StartDate: data.StartDate ? data.StartDate : "",
      EndDate: data.EndDate ? data.EndDate : "",
      ...(data.SearchTerm && { SearchTerm: data.SearchTerm }),
    });

    try {
      if (data.shopId) {
        const response = await apiClient.get<any, T>(
          `${API_PATHS.AFFILIATION.GET_COMMISSTION_ORDER}?${queryParams.toString()}`,
          config
        );
        return response;
      }
    } catch (error: any) {
      handleApiError(error);
    }
  },

  exportExcelCommissionOrder: async <T = any>(data, errorConfig?: ErrorConfig) => {
    const config: any = {
      errorHandling: errorConfig,
      responseType: "blob",
    };

    const queryParams = new URLSearchParams({
      shopId: data.shopId,
      PageNumber: data.PageNumber,
      PageSize: data.PageSize,
      SortBy: data.SortBy,
      SortOrder: data.SortOrder,
      StartDate: data.StartDate,
      EndDate: data.EndDate,
      ...(data.SearchTerm && { SearchTerm: data.SearchTerm }),
    });

    try {
      if (data.shopId) {
        const response = await apiClient.get<any, T>(
          `${API_PATHS.AFFILIATION.EXPORT_EXCEL_COMMISSTION_ORDER}?${queryParams.toString()}`,
          config
        );
        return response;
      }
    } catch (error: any) {
      handleApiError(error);
    }
  },

  getPartnerOverview: async <T = any>(data: any, errorConfig?: ErrorConfig) => {
    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
    };

    const queryParams = new URLSearchParams({
      shopId: data.shopId,
      skip: data.skip.toString(),
      limit: data.limit.toString(),
      fromDate: data.fromDate ? data.fromDate : "",
      toDate: data.toDate ? data.toDate : "",
      ...(data.searchTerm && { nameOrReferalCode: data.searchTerm }),
    });

    try {
      const response = await apiClient.get<any, T>(
        `${API_PATHS.AFFILIATION.PARTNER_OVERVIEW}?${queryParams.toString()}`,
        config
      );
      return response;
    } catch (error: any) {
      handleApiError(error);
    }
  },
  exportExcelPartnerOverview: async <T = any>(data: any, errorConfig?: ErrorConfig) => {
    const config: any = {
      errorHandling: errorConfig,
      responseType: "blob",
    };

    const queryParams = new URLSearchParams({
      shopId: data.shopId,

      fromDate: data.fromDate ? data.fromDate : "",
      toDate: data.toDate ? data.toDate : "",
      ...(data.searchTerm && { nameOrReferalCode: data.searchTerm }),
    });

    try {
      const response = await apiClient.get<any, T>(
        `${API_PATHS.AFFILIATION.PARTNER_OVERVIEW_EXPORT_EXCEL}?${queryParams.toString()}`,
        config
      );
      return response;
    } catch (error: any) {
      handleApiError(error);
    }
  },

  /////////////
  getCommissionReport: async <T = any>(data, errorConfig?: ErrorConfig) => {
    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
    };
    const paging = {
      NameType: "Created",
      SortType: "desc",
      PageSize: data.PageSize,
      PageIndex: data.PageNumber,
      Search: data.Search,
      Name: "Created",
      Sort: "desc",
    };

    const queryParams = new URLSearchParams({
      ShopId: data.ShopId,
      Month: data.Month,
      Year: data.Year,
      "Paging.NameType": paging.NameType,
      "Paging.SortType": paging.SortType,
      "Paging.PageSize": paging.PageSize.toString(),
      "Paging.PageIndex": paging.PageIndex.toString(),
      "Paging.Search": paging.Search || "",
      "Paging.Name": paging.Name,
      "Paging.Sort": paging.Sort,
    });

    try {
      if (data.ShopId) {
        const response = await apiClient.get<any, T>(
          `${API_PATHS.AFFILIATION.GET_COMMISSTION_REPORT}?${queryParams.toString()}`,
          config
        );
        return response;
      }
    } catch (error: any) {
      handleApiError(error);
    }
  },

  exportExcelCommissionReport: async <T = any>(data, errorConfig?: ErrorConfig) => {
    const config: any = {
      errorHandling: errorConfig,
      responseType: "blob",
    };

    const paging = {
      NameType: "Created",
      SortType: "desc",
      PageSize: data.PageSize,
      PageIndex: data.PageNumber,
      Search: data.Search,
      Name: "Created",
      Sort: "desc",
    };
    const queryParams = new URLSearchParams({
      ShopId: data.ShopId,
      Month: data.Month,
      Year: data.Year,
      "Paging.NameType": paging.NameType,
      "Paging.SortType": paging.SortType,
      "Paging.PageSize": paging.PageSize.toString(),
      "Paging.PageIndex": paging.PageIndex.toString(),
      "Paging.Search": paging.Search || "",
      "Paging.Name": paging.Name,
      "Paging.Sort": paging.Sort,
    });

    try {
      if (data.ShopId) {
        const response = await apiClient.get<any, T>(
          `${API_PATHS.AFFILIATION.EXPORT_EXCEL_COMMISSTION_REPORT}?${queryParams.toString()}`,
          config
        );
        return response;
      }
    } catch (error: any) {
      handleApiError(error);
    }
  },
  updateRecruitmentPage: async <T = any>(data: any, errorConfig?: ErrorConfig) => {
    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
    };

    try {
      // Tạo đối tượng FormData
      const formData = new FormData();

      // Thêm các trường dữ liệu vào formData
      for (const key in data) {
        if (data.hasOwnProperty(key)) {
          formData.append(key, data[key]);
        }
      }

      const response = await apiClient.post<any, T>(
        `${API_PATHS.AFFILIATION.UPDATE_RECRUITMENT_PAGE}`,
        formData,
        {
          ...config,
          headers: {
            "Content-Type": "multipart/form-data", // Đặt header cho form-data
          },
        }
      );
      return response;
    } catch (error: any) {
      handleApiError(error);
    }
  },
  getRecruitmentPage: async <T = any>(data: any, errorConfig?: ErrorConfig) => {
    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
    };

    const queryParams = new URLSearchParams({
      shopId: data.shopId,
    });

    try {
      const response = await apiClient.get<any, T>(
        `${API_PATHS.AFFILIATION.GET_RECRUITMENT_PAGE}?${queryParams.toString()}`,
        config
      );
      return response;
    } catch (error: any) {
      handleApiError(error);
    }
  },

  getListBank: async <T = any>(errorConfig?: ErrorConfig) => {
    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
    };

    try {
      const response = await apiClient.get<any, T>(`${API_PATHS.BANK.LIST_BANK}`, config);
      return response;
    } catch (error: any) {
      handleApiError(error);
    }
  },
};

import React, { useState, useEffect } from 'react';
import { Box, Button } from 'zmp-ui';
import { showToast } from 'zmp-sdk/apis';
import { useAuth } from '../hooks/useAuth';
import { userService } from '../utils/userService';

const UserProfile = ({ shopId = null }) => {
  const { user, isAuthenticated } = useAuth();
  const [userInfo, setUserInfo] = useState(null);
  const [loading, setLoading] = useState(false);
  const [referralInfo, setReferralInfo] = useState(null);
  const [userPoints, setUserPoints] = useState(null);

  useEffect(() => {
    if (isAuthenticated) {
      fetchUserData();
    }
  }, [isAuthenticated, shopId]);

  const fetchUserData = async () => {
    setLoading(true);
    try {
      // Fetch user info with shopId
      const userResult = await userService.getCurrentUser(shopId);
      if (userResult.success) {
        setUserInfo(userResult.data);
      }

      // Fetch referral info
      const referralResult = await userService.getReferralInfo();
      if (referralResult.success) {
        setReferralInfo(referralResult.data);
      }

      // Fetch user points
      const pointsResult = await userService.getUserPoints();
      if (pointsResult.success) {
        setUserPoints(pointsResult.data);
      }
    } catch (error) {
      console.error('Error fetching user data:', error);
      showToast({
        message: 'Không thể tải thông tin người dùng',
        type: 'error',
        duration: 2000
      });
    } finally {
      setLoading(false);
    }
  };

  const handleRefresh = () => {
    fetchUserData();
  };

  if (!isAuthenticated) {
    return null;
  }

  if (loading) {
    return (
      <Box className="p-4 text-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-brand-blue mx-auto"></div>
        <p className="mt-2 text-gray-600">Đang tải thông tin...</p>
      </Box>
    );
  }

  return (
    <Box className="bg-white rounded-lg shadow-md p-4 mb-4">
      {/* Header */}
      <Box className="flex justify-between items-center mb-4">
        <h3 className="text-lg font-bold text-brand-blue">Thông tin tài khoản</h3>
        <Button
          size="small"
          onClick={handleRefresh}
          className="bg-gray-100 text-gray-600 px-3 py-1 rounded text-sm"
        >
          Làm mới
        </Button>
      </Box>

      {/* User Basic Info */}
      <Box className="mb-4">
        <Box className="flex items-center mb-3">
          <div className="w-12 h-12 bg-brand-blue rounded-full flex items-center justify-center mr-3">
            <span className="text-white font-bold text-lg">
              {(userInfo?.fullname || userInfo?.name || 'U').charAt(0).toUpperCase()}
            </span>
          </div>
          <div>
            <h4 className="font-semibold text-gray-800">
              {userInfo?.fullname || userInfo?.name || 'Người dùng'}
            </h4>
            <p className="text-sm text-gray-600">
              {userInfo?.phoneNumber || userInfo?.email || 'Chưa cập nhật'}
            </p>
          </div>
        </Box>

        {/* User ID & Shop Info */}
        {userInfo?.id && (
          <Box className="bg-gray-50 rounded p-3 mb-3">
            <p className="text-xs text-gray-500 mb-1">User ID</p>
            <p className="text-sm font-mono text-gray-700">{userInfo.id}</p>
          </Box>
        )}

        {shopId && (
          <Box className="bg-blue-50 rounded p-3 mb-3">
            <p className="text-xs text-blue-600 mb-1">Shop ID</p>
            <p className="text-sm font-mono text-blue-700">{shopId}</p>
          </Box>
        )}
      </Box>

      {/* Points Info */}
      {userPoints && (
        <Box className="mb-4">
          <h5 className="font-semibold text-gray-800 mb-2">Điểm thưởng</h5>
          <Box className="grid grid-cols-2 gap-3">
            <Box className="bg-yellow-50 rounded p-3 text-center">
              <p className="text-2xl font-bold text-yellow-600">
                {userPoints.totalPoints || 0}
              </p>
              <p className="text-xs text-yellow-700">Tổng điểm</p>
            </Box>
            <Box className="bg-green-50 rounded p-3 text-center">
              <p className="text-2xl font-bold text-green-600">
                {userPoints.availablePoints || 0}
              </p>
              <p className="text-xs text-green-700">Điểm khả dụng</p>
            </Box>
          </Box>
        </Box>
      )}

      {/* Referral Info */}
      {referralInfo && (
        <Box className="mb-4">
          <h5 className="font-semibold text-gray-800 mb-2">Thông tin giới thiệu</h5>
          
          {referralInfo.referralCode && (
            <Box className="bg-purple-50 rounded p-3 mb-3">
              <p className="text-xs text-purple-600 mb-1">Mã giới thiệu của bạn</p>
              <p className="text-lg font-bold text-purple-700">{referralInfo.referralCode}</p>
            </Box>
          )}

          <Box className="grid grid-cols-2 gap-3">
            <Box className="text-center">
              <p className="text-xl font-bold text-brand-blue">
                {referralInfo.totalReferrals || 0}
              </p>
              <p className="text-xs text-gray-600">Người đã giới thiệu</p>
            </Box>
            <Box className="text-center">
              <p className="text-xl font-bold text-green-600">
                {referralInfo.totalCommission || 0}
              </p>
              <p className="text-xs text-gray-600">Hoa hồng (VNĐ)</p>
            </Box>
          </Box>
        </Box>
      )}

      {/* Membership Level */}
      {userInfo?.membershipLevel && (
        <Box className="mb-4">
          <h5 className="font-semibold text-gray-800 mb-2">Hạng thành viên</h5>
          <Box className="bg-gradient-to-r from-yellow-400 to-yellow-600 rounded p-3 text-center">
            <p className="text-white font-bold text-lg">
              {userInfo.membershipLevel}
            </p>
          </Box>
        </Box>
      )}

      {/* Additional Info */}
      {userInfo && (
        <Box className="text-xs text-gray-500 space-y-1">
          {userInfo.createdAt && (
            <p>Ngày tham gia: {new Date(userInfo.createdAt).toLocaleDateString('vi-VN')}</p>
          )}
          {userInfo.lastLoginAt && (
            <p>Đăng nhập lần cuối: {new Date(userInfo.lastLoginAt).toLocaleDateString('vi-VN')}</p>
          )}
        </Box>
      )}
    </Box>
  );
};

export default UserProfile;

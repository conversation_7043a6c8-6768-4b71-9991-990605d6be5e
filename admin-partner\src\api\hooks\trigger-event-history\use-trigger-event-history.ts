import { useState } from "react";
import { ErrorHandlerService } from "../../services/error-handler.service";
import {
  ParamGetListTriggerEventHistory,
  userTriggerEventHistoryService,
} from "../../services/trigger-event-history/trigger-event-history.service";

export interface TriggerParameterDto {
  name: string;
  value: string;
  dataType: string;
  orderNumber: number;
}

export const useTriggerEventHistory = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const getListHistorySendMessageByTriggerEvent = async (
    params: ParamGetListTriggerEventHistory
  ) => {
    try {
      setLoading(true);
      setError(null);
      const response = await userTriggerEventHistoryService.getListHistorySendMessageByTriggerEvent(
        params
      );
      return response;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: true,
        logError: true,
      });
      setError(errorResponse.detail);
    } finally {
      setLoading(false);
    }
  };

  return {
    getListHistorySendMessageByTriggerEvent,
    loading,
    error,
  };
};

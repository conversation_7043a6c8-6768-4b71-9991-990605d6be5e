import { SearchType } from "./../../types/voucher.type";
import { apiClient } from "@/src/api/config/api-client";
import { API_PATHS } from "@/src/api/constants/api-paths";
import type { ApiError, ErrorConfig } from "@/src/api/types/error.types";
import type { ExtendedRequestConfig } from "@/src/api/types/api.types";
import { GetProductCategoryRequestBody } from "@/src/api/types/product-category.types";

function handleApiError(error: any): never {
  const status = error.response?.status || 500;
  const errorData = error.response?.data as ApiError;
  const errorMessage = errorData?.detail || errorData?.title || error.message;

  if (error.response) {
    throw {
      ...error,
      message: errorMessage,
    };
  }
  throw error;
}

export const voucherService = {
  getProductCategory: async <T = any>(params, errorConfig?: ErrorConfig) => {
    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
    };

    try {
      const response = await apiClient.get<GetProductCategoryRequestBody, T>(
        `${API_PATHS.PRODUCT_CATEGORY.GET_PRODUCT_CATEGORY}?skip=${params.skip}&limit=${params.limit}&shopId=${params.shopId}&partnerId=${params.partnerId}&categoryType=${params.categoryType}&search=${params.search}`,
        config
      );
      return response;
    } catch (error: any) {
      handleApiError(error);
    }
  },

  createVoucherPromotion: async <T = any>(data: any, errorConfig?: ErrorConfig) => {
    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
    };

    try {
      const response = await apiClient.post<any, T>(
        API_PATHS.VOUCHER.CREATE_VOUCHER_PROMOTION,
        data,
        config
      );
      return response;
    } catch (error: any) {
      handleApiError(error);
    }
  },

  getDetailVoucher: async <T = any>(data: any, errorConfig?: ErrorConfig) => {
    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
    };

    try {
      const response = await apiClient.get<any, T>(
        `${API_PATHS.VOUCHER.DETAIL_VOUCHER}?voucherId=${data}`,
        config
      );
      return response;
    } catch (error: any) {
      handleApiError(error);
    }
  },

  listVoucher: async <T = any>(params, errorConfig?: ErrorConfig) => {
    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
    };
    const queryParams = new URLSearchParams({
      skip: params.skip,
      limit: params.limit,
      shopId: params.shopId,
      searchType: params.searchType,
    });
    
    // Handle array of types
    if (params.type && Array.isArray(params.type)) {
      params.type.forEach((type: string) => {
        queryParams.append("type", type);
      });
    } else if (params.type) {
      queryParams.append("type", params.type);
    }
    
    if (params.search) {
      queryParams.append("search", params.search);
    }
    if (params.statusVoucher) {
      queryParams.append("statusVoucher", params.statusVoucher);
    }
    try {
      const response = await apiClient.get(
        `${API_PATHS.VOUCHER.LIST_VOUCHER}?${queryParams.toString()}`,
        {
          shopId: params.shopId,
          partnerId: params.partnerId,
        },
        config
      );
      return response;
    } catch (error: any) {
      handleApiError(error);
    }
  },

  deleteVoucher: async <T = any>(voucherIds: string[], errorConfig?: ErrorConfig) => {
    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
    };

    try {
      const response = await apiClient.delete<T>(`${API_PATHS.VOUCHER.DELETE_VOUCHER}`, {
        ...config,
        data: voucherIds, // Truyền mảng trong body của request
      });
      return response;
    } catch (error: any) {
      handleApiError(error);
    }
  },
  changeActiveVoucher: async <T = any>(data: any, errorConfig?: ErrorConfig) => {
    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
    };

    try {
      const response = await apiClient.post<any, T>(
        `${API_PATHS.VOUCHER.CHANGE_ACTIVE_VOUCHER}?active=${data.active}`,
        data.voucherIds,
        config
      );
      return response;
    } catch (error: any) {
      handleApiError(error);
    }
  },
  updateVoucherPromotion: async <T = any>(data: any, errorConfig?: ErrorConfig) => {
    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
    };

    try {
      const response = await apiClient.post<any, T>(
        API_PATHS.VOUCHER.UPDATE_VOUCHER_PROMOTION,
        data,
        config
      );
      return response;
    } catch (error: any) {
      handleApiError(error);
    }
  },
  listItemsByItemsIds: async <T = any>(data: any, errorConfig?: ErrorConfig) => {
    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
    };

    try {
      const response = await apiClient.post<any, T>(
        API_PATHS.VOUCHER.LIST_ITEMS_BY_ITEMS_ID,
        { shopId: data.shopId, itemsIds: data.itemsIds },
        config
      );
      return response;
    } catch (error: any) {
      handleApiError(error);
    }
  },

  createVoucherTransport: async <T = any>(data: any, errorConfig?: ErrorConfig) => {
    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
    };

    try {
      const response = await apiClient.post<any, T>(
        API_PATHS.VOUCHER.CREATE_VOUCHER_TRANSPORT,
        data,
        config
      );
      return response;
    } catch (error: any) {
      handleApiError(error);
    }
  },
  updateVoucherTransport: async <T = any>(data: any, errorConfig?: ErrorConfig) => {
    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
    };

    try {
      const response = await apiClient.post<any, T>(
        API_PATHS.VOUCHER.UPDATE_VOUCHER_TRANSPORT,
        data,
        config
      );
      return response;
    } catch (error: any) {
      handleApiError(error);
    }
  },

  searchVoucher: async <T = any>(data: any, errorConfig?: ErrorConfig) => {
    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
    };

    try {
      const response = await apiClient.post<any, T>(
        `${API_PATHS.VOUCHER.SEARCH_VOUCHER}?skip=${data.skip}&limit=${data.limit}`,
        {
          partnerId: data.partnerId,
          shopId: data.shopId,
          userId: data.userId,
          listItems: data.listItems,
        },
        config
      );
      return response;
    } catch (error: any) {
      handleApiError(error);
    }
  },
  validateVoucher: async <T = any>(data: any, errorConfig?: ErrorConfig) => {
    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
    };
    try {
      if (Array.isArray(data?.voucherIds) && data?.voucherIds.length > 0) {
        const response = await apiClient.post<any, T>(
          `${API_PATHS.VOUCHER.VALIDATE_VOUCHER}`,
          data,
          config
        );
        return response;
      }
    } catch (error: any) {
      handleApiError(error);
    }
  },
};

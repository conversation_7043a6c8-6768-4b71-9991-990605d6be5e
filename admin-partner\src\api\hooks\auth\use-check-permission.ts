import { useState, useEffect } from "react";
import { useFunction } from "@/src/api/hooks/function/use-function";
import { useStoreId } from "@/src/hooks/use-store-id";
import { IBodyCheckPermission } from "../../services/function/function.service";

export const useCheckPermission = (permissionToCheck: IBodyCheckPermission) => {
  const { checkPermission } = useFunction();
  const shopId = useStoreId();
  const [hasPermission, setHasPermission] = useState(false);
  const [isAgency, setIsAgency] = useState(false);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const check = async () => {
      if (!shopId) {
        return;
      }

      setLoading(true);
      try {
        const res = await checkPermission(permissionToCheck);
        if (res?.status === 200 && res.data.data) {
          setHasPermission(res.data.data.hasPermission || false);
          setIsAgency(res.data.data.isAgency || false);
        } else {
          setHasPermission(false);
          setIsAgency(false);
        }
      } catch (error) {
        console.error("Permission check failed:", error);
        setHasPermission(false);
        setIsAgency(false);
      } finally {
        setLoading(false);
      }
    };

    check();
  }, [shopId]);

  return { hasPermission, isAgency, loading };
};

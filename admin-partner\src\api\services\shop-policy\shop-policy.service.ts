import { apiClient } from '../../config/api-client';
import { API_PATHS } from '../../constants/api-paths';
import type { ApiError, ErrorConfig } from '../../types/error.types';
import type { ExtendedRequestConfig } from '../../types/api.types';
import { logger } from '@/src/utils/logger';

function handleApiError(error: any): never {
  const status = error.response?.status || 500;
  const errorData = error.response?.data as ApiError;
  const errorMessage = errorData?.detail || errorData?.title || error.message;

  if (error.response) {
    throw {
      ...error,
      message: errorMessage,
    };
  }

  throw error;
}

export const shopPolicyService = {
  getListShopPolicy: async <T = any>(
    shopId: string,
    skip: number,
    limit: number,
    search: string = null,
    errorConfig?: ErrorConfig
  ) => {
    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
    };

    try {
      if (shopId) {
        const response = await apiClient.get<{ skip: number; limit: number; search: string }, T>(
          `${API_PATHS.SHOP_POLICY.GET_SHOP_POLICY}?shopId=${shopId}&skip=${skip}&limit=${limit}`,
          config
        );

        return response;
      }
    } catch (error: any) {
      handleApiError(error);
    }
  },

  createShopPolicy: async <T = any>(data: any, errorConfig?: ErrorConfig) => {
    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
    };

    try {
      const response = await apiClient.post<any, T>(
        API_PATHS.SHOP_POLICY.CREATE_SHOP_POLICY,
        data,
        config
      );

      return response;
    } catch (error: any) {
      handleApiError(error);
    }
  },
};

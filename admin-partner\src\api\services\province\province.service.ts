import { apiClient } from '@/src/api/config/api-client';
import { API_PATHS } from '@/src/api/constants/api-paths';
import type { ApiError, ErrorConfig } from '@/src/api/types/error.types';
import type { ExtendedRequestConfig } from '@/src/api/types/api.types';
import { GetServiceRequest, GetServiceRequestBody } from '@/src/api/types/service.types';

function handleApiError(error: any): never {
  const status = error.response?.status || 500;
  const errorData = error.response?.data as ApiError;
  const errorMessage = errorData?.detail || errorData?.title || error.message;

  if (error.response) {
    throw {
      ...error,
      message: errorMessage,
    };
  }
  throw error;
}

export const provinceService = {
  getProvinces: async <T = any>(errorConfig?: ErrorConfig) => {
    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
    };

    try {
      const response = await apiClient.get<GetServiceRequestBody, T>(
        `${API_PATHS.PROVINCE.GET_PROVINCE}`,
        config
      );
      return response;
    } catch (error: any) {
      handleApiError(error);
    }
  },
  getDistricts: async <T = any>(provinceId: string, errorConfig?: ErrorConfig) => {
    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
    };

    try {
      const response = await apiClient.get<GetServiceRequestBody, T>(
        `${API_PATHS.PROVINCE.GET_DISTRICT}?provinceId=${provinceId}`,
        config
      );
      return response;
    } catch (error: any) {
      handleApiError(error);
    }
  },
  getWards: async <T = any>(districtId: string, errorConfig?: ErrorConfig) => {
    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
    };
    try {
      const response = await apiClient.get<GetServiceRequestBody, T>(
        `${API_PATHS.PROVINCE.GET_WARD}?districtId=${districtId}`,
        config
      );
      return response;
    } catch (error: any) {
      handleApiError(error);
    }
  },
};

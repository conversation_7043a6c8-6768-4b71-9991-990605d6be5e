import { useState } from 'react';
import { useStoreId } from '@/src/hooks/use-store-id';
import { GetArticleCategoryRequest } from '@/src/api/types/article-category.types';
import { articleCategoryService } from '@/src/api/services/dashboard/store/article-category.service';
import { ErrorHandlerService } from '@/src/api/services/error-handler.service';

export const useArticleCategory = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const storeId = useStoreId();

  const getArticleCategory = async (skip: number, limit: number, search: string = '') => {
    try {
      setLoading(true);
      setError(null);
      const params: GetArticleCategoryRequest = {
        shopId: storeId,
        search,
        skip,
        limit
      };
      const response = await articleCategoryService.getArticleCategory(params);
      return response;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: true,
        logError: true
      });
      setError(errorResponse.detail);
      throw errorResponse;
    } finally {
      setLoading(false);
    }
  };

  const createArticleCategory = async (data: any) => {
    try {
      setLoading(true);
      setError(null);
      const response = await articleCategoryService.createArticleCategory(data);
      return response;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: true,
        logError: true
      });
      setError(errorResponse.detail);
      throw errorResponse;
    } finally {
      setLoading(false);
    }
  };

  const updateArticleCategory = async (data: any) => {
    try {
      setLoading(true);
      setError(null);
      const response = await articleCategoryService.updateArticleCategory(data);
      return response;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: true,
        logError: true
      });
      setError(errorResponse.detail);
      throw errorResponse;
    } finally {
      setLoading(false);
    }
  };

  const updateImage = async (articleCategoryId: string, imageFile: File) => {
    try {
      setLoading(true);
      setError(null);
      const response = await articleCategoryService.updateImageArticleCategory(articleCategoryId, imageFile);
      return response;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: true,
        logError: true
      });
      setError(errorResponse.detail);
      throw errorResponse;
    } finally {
      setLoading(false);
    }
  };

  const deleteArticleCategory = async (articleCategoryId: string) => {
    try {
      setLoading(true);
      setError(null);
      const response = await articleCategoryService.deleteArticleCategory(articleCategoryId);
      return response;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: true,
        logError: true
      });
      setError(errorResponse.detail);
      throw errorResponse;
    } finally {
      setLoading(false);
    }
  };

  const getArticleCategoryDetail = async (articleCategoryId: string) => {
    try {
      setLoading(true);
      setError(null);
      const response = await articleCategoryService.getArticleCategoryDetail(articleCategoryId);
      return response;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: true,
        logError: true
      });
      setError(errorResponse.detail);
      throw errorResponse;
    } finally {
      setLoading(false);
    }
  };

  return {
    getArticleCategory,
    createArticleCategory,
    getArticleCategoryDetail,
    updateArticleCategory,
    updateImage,
    deleteArticleCategory,
    loading,
    error,
  };
};

import { apiClient, authAPI } from './api';

// User service for handling user-related API calls
export const userService = {
  // Get current user with shop info
  getCurrentUser: async (shopId = null) => {
    try {
      const response = await authAPI.getCurrentUserInfo(shopId);
      return {
        success: true,
        data: response,
      };
    } catch (error) {
      console.error('Get current user error:', error);
      return {
        success: false,
        error: error.response?.data?.message || 'Không thể lấy thông tin người dùng',
      };
    }
  },

  // Get user shops
  getUserShops: async () => {
    try {
      const response = await apiClient.get('/api/user/shops');
      return {
        success: true,
        data: response.data,
      };
    } catch (error) {
      console.error('Get user shops error:', error);
      return {
        success: false,
        error: error.response?.data?.message || 'Không thể lấy danh sách cửa hàng',
      };
    }
  },

  // Update user profile
  updateProfile: async (profileData) => {
    try {
      const response = await apiClient.put('/api/user/profile', profileData);
      return {
        success: true,
        data: response.data,
        message: 'Cập nhật thông tin thành công',
      };
    } catch (error) {
      console.error('Update profile error:', error);
      return {
        success: false,
        error: error.response?.data?.message || 'Cập nhật thông tin thất bại',
      };
    }
  },

  // Get user referral info
  getReferralInfo: async () => {
    try {
      const response = await apiClient.get('/api/user/referral');
      return {
        success: true,
        data: response.data,
      };
    } catch (error) {
      console.error('Get referral info error:', error);
      return {
        success: false,
        error: error.response?.data?.message || 'Không thể lấy thông tin giới thiệu',
      };
    }
  },

  // Get user points/rewards
  getUserPoints: async () => {
    try {
      const response = await apiClient.get('/api/user/points');
      return {
        success: true,
        data: response.data,
      };
    } catch (error) {
      console.error('Get user points error:', error);
      return {
        success: false,
        error: error.response?.data?.message || 'Không thể lấy thông tin điểm thưởng',
      };
    }
  },

  // Get user orders
  getUserOrders: async (page = 1, limit = 10) => {
    try {
      const response = await apiClient.get(`/api/user/orders?page=${page}&limit=${limit}`);
      return {
        success: true,
        data: response.data,
      };
    } catch (error) {
      console.error('Get user orders error:', error);
      return {
        success: false,
        error: error.response?.data?.message || 'Không thể lấy danh sách đơn hàng',
      };
    }
  },

  // Get user vouchers
  getUserVouchers: async () => {
    try {
      const response = await apiClient.get('/api/user/vouchers');
      return {
        success: true,
        data: response.data,
      };
    } catch (error) {
      console.error('Get user vouchers error:', error);
      return {
        success: false,
        error: error.response?.data?.message || 'Không thể lấy danh sách voucher',
      };
    }
  },

  // Apply referral code
  applyReferralCode: async (referralCode) => {
    try {
      const response = await apiClient.post('/api/user/referral/apply', {
        referralCode: referralCode,
      });
      return {
        success: true,
        data: response.data,
        message: 'Áp dụng mã giới thiệu thành công',
      };
    } catch (error) {
      console.error('Apply referral code error:', error);
      return {
        success: false,
        error: error.response?.data?.message || 'Áp dụng mã giới thiệu thất bại',
      };
    }
  },

  // Get user notifications
  getUserNotifications: async (page = 1, limit = 20) => {
    try {
      const response = await apiClient.get(`/api/user/notifications?page=${page}&limit=${limit}`);
      return {
        success: true,
        data: response.data,
      };
    } catch (error) {
      console.error('Get user notifications error:', error);
      return {
        success: false,
        error: error.response?.data?.message || 'Không thể lấy thông báo',
      };
    }
  },

  // Mark notification as read
  markNotificationAsRead: async (notificationId) => {
    try {
      const response = await apiClient.put(`/api/user/notifications/${notificationId}/read`);
      return {
        success: true,
        data: response.data,
      };
    } catch (error) {
      console.error('Mark notification as read error:', error);
      return {
        success: false,
        error: error.response?.data?.message || 'Không thể đánh dấu đã đọc',
      };
    }
  },
};

// Shop service for handling shop-related API calls
export const shopService = {
  // Get shop info
  getShopInfo: async (shopId) => {
    try {
      const response = await apiClient.get(`/api/shops/${shopId}`);
      return {
        success: true,
        data: response.data,
      };
    } catch (error) {
      console.error('Get shop info error:', error);
      return {
        success: false,
        error: error.response?.data?.message || 'Không thể lấy thông tin cửa hàng',
      };
    }
  },

  // Get shop products
  getShopProducts: async (shopId, page = 1, limit = 20) => {
    try {
      const response = await apiClient.get(`/api/shops/${shopId}/products?page=${page}&limit=${limit}`);
      return {
        success: true,
        data: response.data,
      };
    } catch (error) {
      console.error('Get shop products error:', error);
      return {
        success: false,
        error: error.response?.data?.message || 'Không thể lấy danh sách sản phẩm',
      };
    }
  },

  // Get all shops
  getAllShops: async (page = 1, limit = 20) => {
    try {
      const response = await apiClient.get(`/api/shops?page=${page}&limit=${limit}`);
      return {
        success: true,
        data: response.data,
      };
    } catch (error) {
      console.error('Get all shops error:', error);
      return {
        success: false,
        error: error.response?.data?.message || 'Không thể lấy danh sách cửa hàng',
      };
    }
  },
};

export default {
  userService,
  shopService,
};

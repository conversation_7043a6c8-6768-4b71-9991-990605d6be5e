// Storage utility functions for managing local storage
export const StorageKeys = {
  ACCESS_TOKEN: 'accessToken',
  REFRESH_TOKEN: 'refreshToken',
  USER: 'user',
  REFERRAL_CODE: 'referralCode',
  CART: 'cart',
  SETTINGS: 'settings',
};

// Get item from localStorage
export const getItem = (key) => {
  try {
    const item = localStorage.getItem(key);
    return item ? JSON.parse(item) : null;
  } catch (error) {
    console.error(`Error getting item ${key} from localStorage:`, error);
    return null;
  }
};

// Set item to localStorage
export const setItem = (key, value) => {
  try {
    localStorage.setItem(key, JSON.stringify(value));
    return true;
  } catch (error) {
    console.error(`Error setting item ${key} to localStorage:`, error);
    return false;
  }
};

// Remove item from localStorage
export const removeItem = (key) => {
  try {
    localStorage.removeItem(key);
    return true;
  } catch (error) {
    console.error(`Error removing item ${key} from localStorage:`, error);
    return false;
  }
};

// Clear all localStorage
export const clearStorage = () => {
  try {
    localStorage.clear();
    return true;
  } catch (error) {
    console.error('Error clearing localStorage:', error);
    return false;
  }
};

// Get referral code from URL or storage
export const getReferralCode = () => {
  // First check URL parameters
  const urlParams = new URLSearchParams(window.location.search);
  const refFromUrl = urlParams.get('ref') || urlParams.get('referralCode');
  
  if (refFromUrl) {
    // Store referral code for later use
    setItem(StorageKeys.REFERRAL_CODE, refFromUrl);
    return refFromUrl;
  }
  
  // If not in URL, check localStorage
  return getItem(StorageKeys.REFERRAL_CODE);
};

// Set referral code
export const setReferralCode = (code) => {
  return setItem(StorageKeys.REFERRAL_CODE, code);
};

// Clear referral code
export const clearReferralCode = () => {
  return removeItem(StorageKeys.REFERRAL_CODE);
};

// User-related storage functions
export const getUserFromStorage = () => {
  return getItem(StorageKeys.USER);
};

export const setUserToStorage = (user) => {
  return setItem(StorageKeys.USER, user);
};

export const clearUserFromStorage = () => {
  removeItem(StorageKeys.ACCESS_TOKEN);
  removeItem(StorageKeys.REFRESH_TOKEN);
  removeItem(StorageKeys.USER);
};

// Token-related storage functions
export const getAccessToken = () => {
  return getItem(StorageKeys.ACCESS_TOKEN);
};

export const setAccessToken = (token) => {
  return setItem(StorageKeys.ACCESS_TOKEN, token);
};

export const getRefreshToken = () => {
  return getItem(StorageKeys.REFRESH_TOKEN);
};

export const setRefreshToken = (token) => {
  return setItem(StorageKeys.REFRESH_TOKEN, token);
};

export default {
  StorageKeys,
  getItem,
  setItem,
  removeItem,
  clearStorage,
  getReferralCode,
  setReferralCode,
  clearReferralCode,
  getUserFromStorage,
  setUserToStorage,
  clearUserFromStorage,
  getAccessToken,
  setAccessToken,
  getRefreshToken,
  setRefreshToken,
};

import { useState } from "react";
import { ErrorHandlerService } from "../../services/error-handler.service";
import { zaloTemplateService } from "../../services/zalo-template/zalo-template.service";
interface ZaloAuthUrlResponse {
  url: string;
}

export const useZaloAuth = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchZaloAuthUrl = async (shopId: string): Promise<ZaloAuthUrlResponse | undefined> => {
    if (!shopId) {
      setError("Shop ID is required.");
      return undefined;
    }

    try {
      setLoading(true);
      setError(null);
      const response = await zaloTemplateService.getZaloAuthUrl<{ data: { url: string } }>(shopId);

      console.log("Zalo Auth Hook - Response from service:", response);

      if (response?.data?.data?.url) {
        return { url: response.data.data.url };
      } else {
        console.error("API response missing URL at expected path (response.data.data.url):", response);
        setError("Invalid API response for Zalo auth URL");
        return undefined;
      }
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: false,
        logError: true,
      });
      setError(errorResponse.detail || "Failed to fetch Zalo authorization URL.");
      return undefined;
    } finally {
      setLoading(false);
    }
  };

  return {
    fetchZaloAuthUrl,
    loading,
    error,
    setError,
  };
};

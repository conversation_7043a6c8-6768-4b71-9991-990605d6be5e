// importScripts("https://www.gstatic.com/firebasejs/9.0.0/firebase-app-compat.js");
// importScripts("https://www.gstatic.com/firebasejs/9.0.0/firebase-messaging-compat.js");

// // Listen for environment setting message
// self.addEventListener('message', (event) => {
//   console.log('event: ', event);
//   if (event.data && event.data.type === 'SET_FIREBASE_ENV') {
//     const env = event.data.environment;
//     importScripts(`/firebaseConfig/${env}/firebase-config.json`);

//     // Initialize Firebase after config is loaded
//     firebase.initializeApp(self.firebaseConfig);
//   }
// });

// const messaging = firebase.messaging();

// // Handle background messages
// messaging.onBackgroundMessage((payload) => {
//   console.log("Received background message:", payload);

//   if (!payload.notification) {
//     return;
//   }

//   const { title, body } = payload.notification;
//   const notificationOptions = {
//     body,
//     icon: "/logo.png",
//     badge: "/badge.png",
//     data: payload.data,
//     tag: Date.now().toString(), // Add unique tag to allow multiple notifications
//     actions: [
//       {
//         action: "open_order",
//         title: "Xem chi tiết",
//       },
//     ],
//     // Thêm click_action để handle click event
//     click_action: payload.data?.click_action || "/",
//   };

//   // Add click event listener
//   // self.addEventListener('notificationclick', function(event) {
//   //   event.notification.close();

//   //   // Get the order URL from notification data
//   //   const orderUrl = event.notification.data?.click_action || '/';

//   //   // Open or focus the window with the order details
//   //   event.waitUntil(
//   //     clients.matchAll({type: 'window'}).then(windowClients => {
//   //       // Check if there is already a window/tab open with the target URL
//   //       for (var i = 0; i < windowClients.length; i++) {
//   //         var client = windowClients[i];
//   //         if (client.url === orderUrl && 'focus' in client) {
//   //           return client.focus();
//   //         }
//   //       }
//   //       // If no window/tab is open, open a new one
//   //       if (clients.openWindow) {
//   //         return clients.openWindow(orderUrl);
//   //       }
//   //     })
//   //   );
//   // });

//   return self.registration.showNotification(title, notificationOptions);
// });

import { Page, Box, useNavigate } from "zmp-ui";

function UserVouchersPage() {
  const navigate = useNavigate();

  const handleBackClick = () => {
    navigate(-1); // Go back to previous page
  };

  const handleVoucherClick = () => {
    navigate('/voucher-detail'); // Navigate to voucher detail page
  };

  return (
    <Page className="min-h-screen pb-20" style={{ backgroundColor: '#FFFEF1' }}>
      {/* Header - Ưu đãi của bạn với back button */}
      <Box className="sticky top-0 z-20 px-4 md:px-6 pt-10 pb-3" style={{ backgroundColor: '#FFFEF1' }}>
        <Box className="flex items-center cursor-pointer" onClick={handleBackClick}>
          {/* Back Arrow */}
          <svg className="w-6 h-6 md:w-7 md:h-7 text-gray-800 mr-3 md:mr-4" viewBox="0 0 11 17" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M9 1L1.01743 8.49998L9.03487 16" fill="#161616" />
            <path d="M9 1L1.01743 8.49998L9.03487 16" stroke="#F0F0F0" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
          </svg>

          {/* Header Content */}
          <Box className="flex-1">
            <h1 className="text-lg md:text-xl font-bold text-gray-800">Ưu đãi của bạn</h1>
          </Box>
        </Box>
      </Box>

      {/* Phiếu ưu đãi của bạn Section */}
      <Box className="mb-4 md:mb-6">
        <Box className="bg-white shadow-md p-4 md:p-6">
          {/* Header */}
          <h2 className="text-lg md:text-xl font-bold mb-4 md:mb-6 text-brand-blue">Phiếu ưu đãi của bạn</h2>

          {/* Vouchers List */}
          <Box className="space-y-3 md:space-y-4">
            {/* Voucher 1 - Beauty Shop */}
            <Box className="relative p-3 md:p-4 bg-gray-50 rounded-lg star-offer-card cursor-pointer" onClick={handleVoucherClick}>
              {/* x1 Badge - Top Right */}
              <Box className="absolute top-2 md:top-3 right-2 md:right-3">
                <span className="star-badge px-2 py-1 rounded text-xs md:text-sm font-medium">x1</span>
              </Box>

              {/* Main Content */}
              <Box className="flex items-center min-h-[70px] md:min-h-[80px]">
                {/* Icon */}
                <Box className="flex-shrink-0 flex items-center justify-center" onClick={handleVoucherClick}>
                  <Box className="star-offer-image-bg">
                    <img
                      src="/src/static/beauty-shop.png"
                      alt="Beauty Shop"
                      className="w-12 h-auto md:w-16"
                    />
                  </Box>
                </Box>

                {/* Vertical Dotted Divider */}
                <Box className="vertical-dotted-divider"></Box>

                {/* Content */}
                <Box className="flex-1">
                  <Box className="flex items-center mb-1 md:mb-2">
                    <span className="discount-tag px-2 py-1 rounded text-xs md:text-sm mr-2 whitespace-nowrap">Mã giảm giá</span>
                  </Box>
                  <h3 className="font-bold text-gray-800 mb-1 md:mb-2 text-sm md:text-base whitespace-nowrap">Giảm 20k cho đơn từ 100k</h3>

                  {/* Bottom row with text */}
                  <Box className="flex items-center pr-3 md:pr-4">
                    <Box>
                      <p className="text-xs md:text-sm text-gray-500 whitespace-nowrap">Đơn tối thiểu: 20k</p>
                      <p className="text-xs md:text-sm text-gray-500 whitespace-nowrap">Hết hạng trong: 10 ngày</p>
                    </Box>
                  </Box>
                </Box>
              </Box>

              {/* Action Button - Absolute positioned to right */}
              <Box className="absolute bottom-2 md:bottom-3 right-2 md:right-3">
                <button
                  className="bg-brand-blue text-white px-3 md:px-4 py-1.5 md:py-2 rounded-full text-xs md:text-sm font-medium star-offer-button"
                  onClick={(e) => {
                    e.stopPropagation(); // Prevent card click
                    handleVoucherClick();
                  }}
                >
                  Sử dụng ngay
                </button>
              </Box>
            </Box>

            {/* Voucher 2 - Zalo App */}
            <Box className="relative p-3 md:p-4 bg-gray-50 rounded-lg star-offer-card cursor-pointer" onClick={handleVoucherClick}>
              {/* x1 Badge - Top Right */}
              <Box className="absolute top-2 md:top-3 right-2 md:right-3">
                <span className="star-badge px-2 py-1 rounded text-xs md:text-sm font-medium">x1</span>
              </Box>

              {/* Main Content */}
              <Box className="flex items-center min-h-[70px] md:min-h-[80px]">
                {/* Icon */}
                <Box className="flex-shrink-0 flex items-center justify-center" onClick={handleVoucherClick}>
                  <Box className="star-offer-image-bg">
                    <img
                      src="/src/static/ap-dung-tren-zalo-app.png"
                      alt="Áp dụng trên Zalo app"
                      className="w-12 h-auto md:w-16"
                    />
                  </Box>
                </Box>

                {/* Vertical Dotted Divider */}
                <Box className="vertical-dotted-divider"></Box>

                {/* Content */}
                <Box className="flex-1">
                  <Box className="flex items-center mb-1 md:mb-2">
                    <span className="discount-tag px-2 py-1 rounded text-xs md:text-sm mr-2 whitespace-nowrap">Mã giảm giá</span>
                  </Box>
                  <h3 className="font-bold text-gray-800 mb-1 md:mb-2 text-sm md:text-base whitespace-nowrap">Giảm 20k cho đơn từ 100k</h3>

                  {/* Bottom row with text */}
                  <Box className="flex items-center pr-3 md:pr-4">
                    <Box>
                      <p className="text-xs md:text-sm text-gray-500 whitespace-nowrap">Đơn tối thiểu: 20k</p>
                      <p className="text-xs md:text-sm text-gray-500 whitespace-nowrap">Hết hạng trong: 10 ngày</p>
                    </Box>
                  </Box>
                </Box>
              </Box>

              {/* Action Button - Absolute positioned to right */}
              <Box className="absolute bottom-2 md:bottom-3 right-2 md:right-3">
                <button
                  className="bg-brand-blue text-white px-3 md:px-4 py-1.5 md:py-2 rounded-full text-xs md:text-sm font-medium star-offer-button"
                  onClick={(e) => {
                    e.stopPropagation(); // Prevent card click
                    handleVoucherClick();
                  }}
                >
                  Đổi ngay
                </button>
              </Box>
            </Box>

            {/* Voucher 3 - Beauty Shop */}
            <Box className="relative p-3 md:p-4 bg-gray-50 rounded-lg star-offer-card cursor-pointer" onClick={handleVoucherClick}>
              {/* x1 Badge - Top Right */}
              <Box className="absolute top-2 md:top-3 right-2 md:right-3">
                <span className="star-badge px-2 py-1 rounded text-xs md:text-sm font-medium">x1</span>
              </Box>

              {/* Main Content */}
              <Box className="flex items-center min-h-[70px] md:min-h-[80px]">
                {/* Icon */}
                <Box className="flex-shrink-0 flex items-center justify-center" onClick={handleVoucherClick}>
                  <Box className="star-offer-image-bg">
                    <img
                      src="/src/static/beauty-shop.png"
                      alt="Beauty Shop"
                      className="w-12 h-auto md:w-16"
                    />
                  </Box>
                </Box>

                {/* Vertical Dotted Divider */}
                <Box className="vertical-dotted-divider"></Box>

                {/* Content */}
                <Box className="flex-1">
                  <Box className="flex items-center mb-1 md:mb-2">
                    <span className="discount-tag px-2 py-1 rounded text-xs md:text-sm mr-2 whitespace-nowrap">Mã giảm giá</span>
                  </Box>
                  <h3 className="font-bold text-gray-800 mb-1 md:mb-2 text-sm md:text-base whitespace-nowrap">Giảm 20k cho đơn từ 100k</h3>

                  {/* Bottom row with text */}
                  <Box className="flex items-center pr-3 md:pr-4">
                    <Box>
                      <p className="text-xs md:text-sm text-gray-500 whitespace-nowrap">Đơn tối thiểu: 20k</p>
                      <p className="text-xs md:text-sm text-gray-500 whitespace-nowrap">Hết hạng trong: 10 ngày</p>
                    </Box>
                  </Box>
                </Box>
              </Box>

              {/* Action Button - Absolute positioned to right */}
              <Box className="absolute bottom-2 md:bottom-3 right-2 md:right-3">
                <button
                  className="bg-brand-blue text-white px-3 md:px-4 py-1.5 md:py-2 rounded-full text-xs md:text-sm font-medium star-offer-button"
                  onClick={(e) => {
                    e.stopPropagation(); // Prevent card click
                    handleVoucherClick();
                  }}
                >
                  Sử dụng ngay
                </button>
              </Box>
            </Box>

            {/* Voucher 4 - Zalo App */}
            <Box className="relative p-3 md:p-4 bg-gray-50 rounded-lg star-offer-card cursor-pointer" onClick={handleVoucherClick}>
              {/* x1 Badge - Top Right */}
              <Box className="absolute top-2 md:top-3 right-2 md:right-3">
                <span className="star-badge px-2 py-1 rounded text-xs md:text-sm font-medium">x1</span>
              </Box>

              {/* Main Content */}
              <Box className="flex items-center min-h-[70px] md:min-h-[80px]">
                {/* Icon */}
                <Box className="flex-shrink-0 flex items-center justify-center" onClick={handleVoucherClick}>
                  <Box className="star-offer-image-bg">
                    <img
                      src="/src/static/ap-dung-tren-zalo-app.png"
                      alt="Áp dụng trên Zalo app"
                      className="w-12 h-auto md:w-16"
                    />
                  </Box>
                </Box>

                {/* Vertical Dotted Divider */}
                <Box className="vertical-dotted-divider"></Box>

                {/* Content */}
                <Box className="flex-1">
                  <Box className="flex items-center mb-1 md:mb-2">
                    <span className="discount-tag px-2 py-1 rounded text-xs md:text-sm mr-2 whitespace-nowrap">Mã giảm giá</span>
                  </Box>
                  <h3 className="font-bold text-gray-800 mb-1 md:mb-2 text-sm md:text-base whitespace-nowrap">Giảm 20k cho đơn từ 100k</h3>

                  {/* Bottom row with text */}
                  <Box className="flex items-center pr-3 md:pr-4">
                    <Box>
                      <p className="text-xs md:text-sm text-gray-500 whitespace-nowrap">Đơn tối thiểu: 20k</p>
                      <p className="text-xs md:text-sm text-gray-500 whitespace-nowrap">Hết hạng trong: 10 ngày</p>
                    </Box>
                  </Box>
                </Box>
              </Box>

              {/* Action Button - Absolute positioned to right */}
              <Box className="absolute bottom-2 md:bottom-3 right-2 md:right-3">
                <button
                  className="bg-brand-blue text-white px-3 md:px-4 py-1.5 md:py-2 rounded-full text-xs md:text-sm font-medium star-offer-button"
                  onClick={(e) => {
                    e.stopPropagation(); // Prevent card click
                    handleVoucherClick();
                  }}
                >
                  Đổi ngay
                </button>
              </Box>
            </Box>
          </Box>
        </Box>
      </Box>
    </Page>
  );
}

export default UserVouchersPage;

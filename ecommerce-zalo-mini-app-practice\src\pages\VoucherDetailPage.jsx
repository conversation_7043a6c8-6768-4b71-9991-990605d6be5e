import { Page, Box, useNavigate } from "zmp-ui";
import { useState } from "react";
import beautyShopImage from "../static/beauty-shop.png";
import exchangeSuccessImage from "../static/exchange-success.png";

function VoucherDetailPage() {
  const navigate = useNavigate();
  const [showSuccessModal, setShowSuccessModal] = useState(false);

  const handleBackClick = () => {
    navigate(-1); // Go back to previous page (UserVouchersPage)
  };

  const handleUseVoucher = () => {
    // Show success modal
    setShowSuccessModal(true);
  };

  const handleCloseSuccessModal = () => {
    setShowSuccessModal(false);
  };

  const handleViewVouchers = () => {
    setShowSuccessModal(false);
    // Navigate to vouchers page or stay on current page
  };

  const handleLearnMore = () => {
    // Handle learn more action
    console.log("Tìm hiểu thêm");
  };

  return (
    <Page className="min-h-screen page-content-safe" style={{ backgroundColor: '#FFFEF1' }}>
      {/* Header - Chi tiết Ưu đãi với back button và menu */}
      <Box className="sticky top-0 z-20 px-4 md:px-6 pt-10 pb-3" style={{ backgroundColor: '#FFFEF1' }}>
        <Box className="flex items-center justify-between">
          {/* Back button */}
          <Box className="flex items-center cursor-pointer" onClick={handleBackClick}>
            <svg className="w-6 h-6 md:w-7 md:h-7 text-gray-800 mr-3 md:mr-4" viewBox="0 0 11 17" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M9 1L1.01743 8.49998L9.03487 16" fill="#161616" />
              <path d="M9 1L1.01743 8.49998L9.03487 16" stroke="#F0F0F0" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
            </svg>
            <h1 className="text-lg md:text-xl font-bold text-gray-800">Chi tiết Ưu đãi</h1>
          </Box>
        </Box>
      </Box>

      {/* Main Voucher Image */}
      <Box className="relative">
        <Box className="flex items-center justify-between mb-4">
          <img
            src={beautyShopImage}
            alt="Beauty Shop"
            className="w-full"
          />
        </Box>
      </Box>

      {/* Voucher Details Card - Figma Design */}
      <Box className="px-4 md:px-6 mb-4 md:mb-6 -mt-8 md:-mt-10 relative z-10">
        <Box className="bg-white overflow-hidden relative" style={{ borderRadius: '2px', boxShadow: '0px 4px 12px 0px rgba(0, 0, 0, 0.1)', height: '120px', minHeight: '120px' }}>
          {/* Left and Right Notches */}
          <Box
            className="absolute left-0 top-1/2 transform -translate-y-1/2 rounded-full"
            style={{ width: '12px', height: '12px', marginLeft: '-6px', backgroundColor: '#F5F5F5' }}
          ></Box>
          <Box
            className="absolute right-0 top-1/2 transform -translate-y-1/2 rounded-full"
            style={{ width: '12px', height: '12px', marginRight: '-6px', backgroundColor: '#F5F5F5' }}
          ></Box>

          {/* Content Container */}
          <Box className="h-full flex">
            {/* Left Image Section */}
            <Box className="flex-shrink-0 flex items-center justify-center" style={{ width: '90px', margin: '4px', borderRadius: '2px', backgroundColor: '#F5F5F5' }}>
              <Box className="relative overflow-hidden" style={{ width: '75px', height: '45px', borderRadius: '2px' }}>
                <img
                  src={beautyShopImage}
                  alt="Beauty Shop"
                  className="w-full h-full object-cover"
                />
              </Box>
            </Box>

            {/* Vertical Dotted Divider */}
            <Box className="relative flex items-center" style={{ width: '12px' }}>
              <Box
                className="absolute left-1/2 transform -translate-x-1/2"
                style={{
                  width: '0px',
                  height: '90px',
                  borderLeft: '2px dashed #EDEDED'
                }}
              ></Box>
            </Box>

            {/* Right Content Section */}
            <Box className="flex-1 relative overflow-hidden">
              {/* Top-right Badge */}
              <Box
                className="absolute top-0 right-0 bg-brand-blue text-white flex items-center justify-center text-center font-bold overflow-hidden"
                style={{
                  borderRadius: '2px 2px 0px 0px',
                  width: '70px',
                  height: '22px',
                  fontSize: '10px',
                  lineHeight: '1.1'
                }}
              >
                <span className="truncate px-1">x1 còn lại</span>
              </Box>

              {/* Content Area */}
              <Box className="flex items-center h-full pl-2 md:pl-4 pr-1" style={{ paddingRight: '72px' }}>
                <Box className="flex flex-col justify-center w-full">
                  {/* Category Badge */}
                  <Box className="mb-1 md:mb-2">
                    <span
                      className="inline-block text-brand-blue border border-brand-blue px-2 py-0.5 md:py-1 font-normal"
                      style={{
                        fontSize: '10px',
                        lineHeight: '1.1',
                        borderRadius: '3px'
                      }}
                    >
                      Mã tích điểm
                    </span>
                  </Box>

                  {/* Title */}
                  <h2
                    className="text-black font-bold"
                    style={{
                      fontSize: '13px',
                      lineHeight: '1.2',
                      fontWeight: '700',
                      marginTop: '4px',
                      marginBottom: '2px'
                    }}
                  >
                    Tích điểm ngẫu nhiên
                  </h2>

                  {/* Code */}
                  <p
                    style={{
                      fontSize: '11px',
                      lineHeight: '1.2',
                      color: '#666666',
                      fontWeight: '500',
                      marginBottom: '2px'
                    }}
                  >
                    #ABCDEF123456
                  </p>

                  {/* Expiry Date */}
                  <p
                    style={{
                      fontSize: '10px',
                      lineHeight: '1.2',
                      color: '#999999',
                      fontWeight: '400'
                    }}
                  >
                    HSD: 12/12/2024
                  </p>
                </Box>
              </Box>
            </Box>
          </Box>
        </Box>
      </Box>

      {/* Details Sections */}
      <Box className="px-4 space-y-6">
        {/* Hạn sử dụng mã */}
        <Box>
          <h3 className="font-bold text-gray-800 mb-2">Hạn sử dụng mã</h3>
          <p className="text-sm text-gray-600">12 Th12 2024 12:30 - 12 Th12 2024 23:59</p>
        </Box>

        {/* Ưu đãi */}
        <Box>
          <h3 className="font-bold text-gray-800 mb-2">Ưu đãi</h3>
          <p className="text-sm text-gray-600">Lượt sử dụng có hạn. Nhanh tay kéo lô bạn nhé! Giảm 12% Đơn Tối Thiểu đ1tr Giảm tối đa đ1tr</p>
        </Box>

        {/* Áp dụng cho sản phẩm */}
        <Box>
          <h3 className="font-bold text-gray-800 mb-2">Áp dụng cho sản phẩm</h3>
          <p className="text-sm text-gray-600 mb-2">
            Chỉ áp dụng trên App cho một số sản phẩm và một số người dùng tham gia chương trình khuyến mãi nhất định.
          </p>
          <p className="text-sm text-gray-600 mb-2">
            - Trong các sản phẩm đã chọn có một số sản phẩm không được chạy khuyến mãi theo quy định của pháp luật hoặc đây là sản phẩm độc quyền dành cho thành viên.
          </p>
          <button
            onClick={handleLearnMore}
            className="text-brand-blue text-sm font-medium underline"
          >
            Tìm hiểu thêm.
          </button>
        </Box>

        {/* Thanh toán */}
        <Box>
          <h3 className="font-bold text-gray-800 mb-2">Thanh toán</h3>
          <p className="text-sm text-gray-600">Tất cả các hình thức thanh toán</p>
        </Box>
      </Box>

      {/* Bottom Action Button */}
      <Box className="bottom-0 left-0 right-0 p-4 bg-white border-t border-gray-200 shadow-md">
        <Box className="max-w-sm mx-auto">
          <button
            onClick={handleUseVoucher}
            className="w-full bg-brand-blue text-white py-4 rounded-full text-lg font-bold hover:bg-blue-700 transition-colors"
          >
            Dùng ngay
          </button>
        </Box>
      </Box>

      {/* Success Modal */}
      {showSuccessModal && (
        <Box className="fixed inset-0 z-50 flex items-center justify-center p-4">
          {/* Overlay */}
          <Box
            className="absolute inset-0 bg-black bg-opacity-50"
            onClick={handleCloseSuccessModal}
          ></Box>

          {/* Success Modal Content */}
          <Box className="relative w-full px-4">
            {/* Modal Card */}
            <Box className="bg-white rounded-2xl p-6 w-full max-w-xs mx-auto text-center">
              {/* Success Illustration */}
              <Box className="mb-8 flex justify-center">
                <img
                  src={exchangeSuccessImage}
                  alt="Exchange Success"
                  className="w-32 h-32 object-contain"
                />
              </Box>

              {/* Success Message */}
              <Box className="mb-8">
                <h2 className="text-sm font-normal text-gray-800 mb-2">Bạn đã đổi</h2>
                <h3 className="text-3xl font-bold text-brand-blue mb-2">100 điểm</h3>
                <p className="text-sm font-normal text-gray-800 mb-4">lấy voucher thành công</p>
                <p className="text-xs text-gray-400 leading-relaxed">
                  Xem lại kho voucher của mình
                </p>
              </Box>

              {/* Action Button */}
              <button
                onClick={handleViewVouchers}
                className="w-full bg-brand-blue text-white py-3 rounded-full text-sm font-medium hover:bg-blue-700 transition-colors"
              >
                Xem ngay
              </button>
            </Box>

            {/* Close Button - Outside Modal */}
            <Box className="flex justify-center mt-6">
              <button
                onClick={handleCloseSuccessModal}
                className="w-12 h-12 bg-gray-400 rounded-full flex items-center justify-center hover:bg-gray-500 transition-colors"
              >
                <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </Box>
          </Box>
        </Box>
      )}
    </Page>
  );
}

export default VoucherDetailPage;
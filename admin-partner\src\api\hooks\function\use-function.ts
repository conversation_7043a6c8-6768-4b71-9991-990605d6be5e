import { useState } from "react";
import { ErrorHandlerService } from "../../services/error-handler.service";

import {
  FunctionPartnerDto,
  functionService,
  IBodyCheckPermission,
} from "../../services/function/function.service";

export const useFunction = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const purchasePackage = async (data: FunctionPartnerDto) => {
    try {
      setLoading(true);
      setError(null);
      const response = await functionService.purchasePackage(data);
      return response;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: true,
        logError: true,
      });
      setError(errorResponse.detail);
    } finally {
      setLoading(false);
    }
  };

  const getActivePackageFunctions = async () => {
    try {
      setLoading(true);
      setError(null);
      const response = await functionService.getActivePackageFunctions();
      return response;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: true,
        logError: true,
      });
      setError(errorResponse.detail);
    } finally {
      setLoading(false);
    }
  };

  const getAvailablePackages = async () => {
    try {
      setLoading(true);
      setError(null);
      const response = await functionService.getAvailablePackages();
      return response;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: true,
        logError: true,
      });
      setError(errorResponse.detail);
    } finally {
      setLoading(false);
    }
  };

  const checkPermission = async (data: IBodyCheckPermission) => {
    try {
      setLoading(true);
      setError(null);
      const response = await functionService.checkPermission(data);
      return response;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: true,
        logError: true,
      });
      setError(errorResponse.detail);
    } finally {
      setLoading(false);
    }
  };

  const getPackageHistory = async () => {
    try {
      setLoading(true);
      setError(null);
      const response = await functionService.getPackageHistory();
      return response;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: true,
        logError: true,
      });
      setError(errorResponse.detail);
    } finally {
      setLoading(false);
    }
  };

  const upgradePackage = async (data: FunctionPartnerDto) => {
    try {
      setLoading(true);
      setError(null);
      const response = await functionService.upgradePackage(data);
      return response;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: true,
        logError: true,
      });
      setError(errorResponse.detail);
    } finally {
      setLoading(false);
    }
  };

  const getAllPermissions = async () => {
    try {
      setLoading(true);
      setError(null);
      const response = await functionService.getAllPermissions();
      return response;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: true,
        logError: true,
      });
      setError(errorResponse.detail);
    } finally {
      setLoading(false);
    }
  };

  return {
    purchasePackage,
    getActivePackageFunctions,
    getAvailablePackages,
    checkPermission,
    getPackageHistory,
    upgradePackage,
    getAllPermissions,
    loading,
    error,
  };
};

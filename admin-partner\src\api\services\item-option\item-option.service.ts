import { apiClient } from '@/src/api/config/api-client';
import { API_PATHS } from '@/src/api/constants/api-paths';
import type { ApiError, ErrorConfig } from '@/src/api/types/error.types';
import type { ExtendedRequestConfig } from '@/src/api/types/api.types';
import { GetProductRequest, GetProductRequestBody } from '@/src/api/types/product.types';

function handleApiError(error: any): never {
  const status = error.response?.status || 500;
  const errorData = error.response?.data as ApiError;
  const errorMessage = errorData?.detail || errorData?.title || error.message;

  if (error.response) {
    throw {
      ...error,
      message: errorMessage,
    };
  }
  throw error;
}

export const itemOptionService = {
  listItemOptionByGroupId: async <T = any>(params, errorConfig?: ErrorConfig) => {
    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
    };
    const queryParams = new URLSearchParams({
      skip: params.skip,
      limit: params.limit,
    });

    try {
      const response = await apiClient.get(
        `${API_PATHS.ITEM_OPTION.LIST_ITEM_OPTION_BY_GROUP_ID.replace(
          '{itemOptionGroupId}',
          params.data?.itemOptionGroupId
        )}?${queryParams.toString()}`,
        params,
        config
      );

      return response;
    } catch (error: any) {
      handleApiError(error);
    }
  },
  itemOptionUser: async <T = any>(params, errorConfig?: ErrorConfig) => {
    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
    };
    try {
      const response = await apiClient.post(
        `${API_PATHS.ITEM_OPTION.ITEM_OPTION_USER}`,
        params,
        config
      );

      return response;
    } catch (error: any) {
      handleApiError(error);
    }
  },

  listItemOptionByIds: async <T = any>(params, errorConfig?: ErrorConfig) => {
    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
    };
    try {
      const response = await apiClient.post(
        `${API_PATHS.ITEM_OPTION.ITEM_OPTION_BY_IDS}`,
        params,
        config
      );

      return response;
    } catch (error: any) {
      handleApiError(error);
    }
  },

  createItemOption: async <T = any>(data, errorConfig?: ErrorConfig) => {
    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
    };
    try {
      const response = await apiClient.post(
        `${API_PATHS.ITEM_OPTION.URL_ITEM_OPTION}`,
        data,
        config
      );

      return response;
    } catch (error: any) {
      handleApiError(error);
    }
  },

  updateItemOption: async <T = any>(id: string, data, errorConfig?: ErrorConfig) => {
    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
    };
    try {
      const response = await apiClient.put(
        `${API_PATHS.ITEM_OPTION.URL_ITEM_OPTION}/${id}`,
        data,
        config
      );

      return response;
    } catch (error: any) {
      handleApiError(error);
    }
  },

  deleteItemOption: async <T = any>(id, errorConfig?: ErrorConfig) => {
    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
    };
    try {
      const response = await apiClient.delete(
        `${API_PATHS.ITEM_OPTION.URL_ITEM_OPTION}/${id}`,
        config
      );
      return response;
    } catch (error: any) {
      handleApiError(error);
    }
  },
};

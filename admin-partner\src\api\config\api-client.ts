import { ApiClient, TokenService } from 'nextjs-api-lib';
import { authService } from '../services/auth/auth.service';

export const ApiConfig = {
  baseURL: process.env.NEXT_PUBLIC_API_URL,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
  handshake: {
    enabled: true,
    type: 'partner' as const,
  },
  storage: {
    enabled: true,
    type: 'localStorage' as const,
  },
  debug: true,
};

// Khởi tạo ApiClient instance
const apiClient = new ApiClient(ApiConfig);

// Lấy instance của Axios
const axiosInstance = apiClient.getInstance();

// Thêm function để update Authorization header
export const updateAuthHeader = (token: string) => {
  if (token) {
    axiosInstance.defaults.headers.common['Authorization'] = `Bearer ${token}`;
  } else {
    delete axiosInstance.defaults.headers.common['Authorization'];
  }
};

// Add response interceptor
axiosInstance.interceptors.response.use(
  (response) => response,
  async (error) => {
    const originalRequest = error.config;

    // Nếu lỗi 401 và chưa thử refresh token
    if (error.response?.status === 401 && !originalRequest._retry) {
      originalRequest._retry = true;

      try {
        const refreshToken = TokenService.getRefreshToken('partner');
        if (!refreshToken) {
          throw new Error('No refresh token available');
        }

        // Lấy token mới
        const response = await authService.refreshToken(refreshToken);

        if (response.data) {
          const { accessToken, refreshToken: newRefreshToken } = response.data;

          // Cập nhật tokens
          await apiClient.setTokens(accessToken, newRefreshToken);

          // Sử dụng function mới để update header
          updateAuthHeader(accessToken);

          originalRequest.headers.Authorization = `Bearer ${accessToken}`;
          return axiosInstance.request(originalRequest);
        }
      } catch (refreshError) {
        // Nếu refresh token thất bại, logout
        apiClient.clearAuth();
        updateAuthHeader(''); // Clear Authorization header

        // Redirect về trang login
        if (typeof window !== 'undefined') {
          window.location.href = '/auth/login';
        }

        return Promise.reject(refreshError);
      }
    }

    return Promise.reject(error);
  }
);

export { apiClient };

import { useEffect, useState } from "react";
import { useStoreId } from "@/src/hooks/use-store-id";

import { ErrorHandlerService } from "@/src/api/services/error-handler.service";
import { StorageService } from "nextjs-api-lib";
import { orderService } from "@/src/api/services/order/order.service";
import { TableOrderFilterType } from "@/src/components/orders/draft/TableOrder";

export const useOrder = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  // const partnerId = StorageService.get('partnerId') as string | null;
  const createOrder = async (data: any) => {
    try {
      setLoading(true);
      setError(null);
      const response = await orderService.createOrder(data);
      return response;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: true,
        logError: true,
      });
      setError(errorResponse.detail);
    } finally {
      setLoading(false);
    }
  };

  const listOrder = async (skip: number, limit: number, data: TableOrderFilterType) => {
    try {
      setLoading(true);
      setError(null);
      const params = {
        data,
        skip,
        limit,
      };
      const response = await orderService.listOrder(params);
      return response;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: true,
        logError: true,
      });
      setError(errorResponse.detail);
    } finally {
      setLoading(false);
    }
  };

  const getOrder = async (data: any) => {
    try {
      setLoading(true);
      setError(null);
      const response = await orderService.getOrder(data);
      return response;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: true,
        logError: true,
      });
      setError(errorResponse.detail);
    } finally {
      setLoading(false);
    }
  };
  const completeShippingItems = async (data: any) => {
    try {
      setLoading(true);
      setError(null);
      const response = await orderService.completeShippingItems(data);
      return response;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: true,
        logError: true,
      });
      setError(errorResponse.detail);
    } finally {
      setLoading(false);
    }
  };
  const updateNotes = async (data: any) => {
    try {
      setLoading(true);
      setError(null);
      const response = await orderService.updateNotes(data);
      return response;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: true,
        logError: true,
      });
      setError(errorResponse.detail);
    } finally {
      setLoading(false);
    }
  };
  const cancelOrder = async (data: any) => {
    try {
      setLoading(true);
      setError(null);
      const response = await orderService.cancelOrder(data);
      return response;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: true,
        logError: true,
      });
      setError(errorResponse.detail);
    } finally {
      setLoading(false);
    }
  };

  const updatePaidOrder = async (data: any) => {
    try {
      setLoading(true);
      setError(null);
      const response = await orderService.updatePaidOrder(data);
      return response;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: true,
        logError: true,
      });
      setError(errorResponse.detail);
    } finally {
      setLoading(false);
    }
  };

  const listOrderByUserId = async (skip: number, limit: number, data: TableOrderFilterType) => {
    try {
      setLoading(true);
      setError(null);
      const params = {
        data,
        skip,
        limit,
      };
      const response = await orderService.listOrderByUserId(params);
      return response;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: true,
        logError: true,
      });
      setError(errorResponse.detail);
    } finally {
      setLoading(false);
    }
  };

  const printTransportOrders = async (data: any) => {
    try {
      setLoading(true);
      setError(null);
      const response = await orderService.printTransportOrders(data);
      return response;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: true,
        logError: true,
      });
      setError(errorResponse.detail);
    } finally {
      setLoading(false);
    }
  };

  const exportOrderExcel = async (data: any) => {
    try {
      setLoading(true);
      setError(null);
      const response = await orderService.exportOrderExcel(data);
      return response;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: true,
        logError: true,
      });
      setError(errorResponse.detail);
    } finally {
      setLoading(false);
    }
  };

  return {
    createOrder,
    listOrder,
    getOrder,
    cancelOrder,
    updateNotes,
    completeShippingItems,
    updatePaidOrder,
    listOrderByUserId,
    printTransportOrders,
    exportOrderExcel,
    loading,
    error,
  };
};

import { useState } from "react";
import { IBodyUpdateShopTaxRate, shopService } from "../../services/shop/shop.service";
import { ErrorHandlerService } from "../../services/error-handler.service";

export const useShop = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const getShop = async (skip: number, limit: number, search: string = null) => {
    try {
      setLoading(true);
      setError(null);
      const response = await shopService.getShop(skip, limit, search);
      return response;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: true,
        logError: true,
      });
      setError(errorResponse.detail);
      throw errorResponse;
    } finally {
      setLoading(false);
    }
  };

  const createShop = async (data: any) => {
    try {
      setLoading(true);
      setError(null);
      const response = await shopService.createShop(data);
      return response;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: true,
        logError: true,
      });
      setError(errorResponse.detail);
      throw errorResponse;
    } finally {
      setLoading(false);
    }
  };

  const updateLogo = async (shopId: string, logoFile: File) => {
    try {
      setLoading(true);
      setError(null);

      const response = await shopService.updateLogoShop(shopId, logoFile);
      return response;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: true,
        logError: true,
      });
      setError(errorResponse.detail);
      throw errorResponse;
    } finally {
      setLoading(false);
    }
  };

  const updateShop = async (data: any) => {
    try {
      setLoading(true);
      setError(null);
      const response = await shopService.updateShop(data);
      return response;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: true,
        logError: true,
      });
      setError(errorResponse.detail);
      throw errorResponse;
    } finally {
      setLoading(false);
    }
  };

  const detailShop = async (shopId: string) => {
    try {
      setLoading(true);
      setError(null);
      if (shopId) {
        const response = await shopService.detailShop(shopId);
        return response;
      }
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: true,
        logError: true,
      });
      setError(errorResponse.detail);
      throw errorResponse;
    } finally {
      setLoading(false);
    }
  };

  const deleteShop = async (shopId: string) => {
    try {
      setLoading(true);
      setError(null);
      const response = await shopService.deleteShop(shopId);
      return response;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: true,
        logError: true,
      });
      setError(errorResponse.detail);
      throw errorResponse;
    } finally {
      setLoading(false);
    }
  };

  const updateShopDelivery = async (data: any) => {
    try {
      setLoading(true);
      setError(null);
      const response = await shopService.updateShopDelivery(data);
      return response;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: true,
        logError: true,
      });
      setError(errorResponse.detail);
      throw errorResponse;
    } finally {
      setLoading(false);
    }
  };
  const updateShopTaxRate = async (data: IBodyUpdateShopTaxRate) => {
    try {
      setLoading(true);
      setError(null);
      const response = await shopService.updateShopTaxRate(data);
      return response;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: true,
        logError: true,
      });
      setError(errorResponse.detail);
      throw errorResponse;
    } finally {
      setLoading(false);
    }
  };

  return {
    getShop,
    createShop,
    updateLogo,
    updateShop,
    detailShop,
    deleteShop,
    updateShopDelivery,
    updateShopTaxRate,
    loading,
    error,
  };
};

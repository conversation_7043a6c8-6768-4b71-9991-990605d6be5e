import { useState } from "react";
import { ErrorHandlerService } from "../../services/error-handler.service";
import {
  IBodyTemplateZns,
  zaloAutomationService,
} from "../../services/zalo-automation/zalo-automation.service";

export const useZaloAutomation = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const getMessageReport = async (filter: any) => {
    try {
      setLoading(true);
      setError(null);
      const response = await zaloAutomationService.getMessageReport(filter);
      return response;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: true,
        logError: true,
      });
      setError(errorResponse.detail);
    } finally {
      setLoading(false);
    }
  };

  const exportSentMessageReport = async (filter: any) => {
    try {
      setLoading(true);
      setError(null);
      const response = await zaloAutomationService.exportSentMessageReport(filter);
      return response;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: true,
        logError: true,
      });
      setError(errorResponse.detail);
    } finally {
      setLoading(false);
    }
  };

  const getListTemplateZns = async (shopId: string, templateName: string, templateId: string) => {
    try {
      setLoading(true);
      setError(null);
      const response = await zaloAutomationService.getListTemplateZns(
        shopId,
        templateName,
        templateId
      );
      return response;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: true,
        logError: true,
      });
      setError(errorResponse.detail);
    } finally {
      setLoading(false);
    }
  };
  const getDetailTemplateZns = async (shopId: string, id: string) => {
    try {
      setLoading(true);
      setError(null);
      const response = await zaloAutomationService.getDetailTemplateZns(shopId, id);
      return response;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: true,
        logError: true,
      });
      setError(errorResponse.detail);
    } finally {
      setLoading(false);
    }
  };
  const exportFileTemplateExcelZns = async (shopId: string, id: string) => {
    try {
      setLoading(true);
      setError(null);
      const response = await zaloAutomationService.exportFileTemplateExcelZns(shopId, id);
      return response;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: true,
        logError: true,
      });
      setError(errorResponse.detail);
    } finally {
      setLoading(false);
    }
  };
  const createTemplateZns = async (data: IBodyTemplateZns) => {
    try {
      setLoading(true);
      setError(null);
      const response = await zaloAutomationService.createTemplateZns(data);
      return response;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: true,
        logError: true,
      });
      setError(errorResponse.detail);
    } finally {
      setLoading(false);
    }
  };
  const deleteTemplateZns = async (shopId: string, id: string) => {
    try {
      setLoading(true);
      setError(null);
      const response = await zaloAutomationService.deleteTemplateZns(shopId, id);
      return response;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: true,
        logError: true,
      });
      setError(errorResponse.detail);
    } finally {
      setLoading(false);
    }
  };
  const getTemplateZnsFromZalo = async (shopId: string) => {
    try {
      setLoading(true);
      setError(null);
      const response = await zaloAutomationService.getTemplateZnsFromZalo(shopId);
      return response;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: true,
        logError: true,
      });
      setError(errorResponse.detail);
    } finally {
      setLoading(false);
    }
  };

  return {
    getMessageReport,
    exportSentMessageReport,
    getListTemplateZns,
    getDetailTemplateZns,
    exportFileTemplateExcelZns,
    createTemplateZns,
    deleteTemplateZns,
    getTemplateZnsFromZalo,
    loading,
    error,
  };
};

import { apiClient } from '@/src/api/config/api-client';
import { API_PATHS } from '@/src/api/constants/api-paths';
import type { ApiError, ErrorConfig } from '@/src/api/types/error.types';
import type { ExtendedRequestConfig } from '@/src/api/types/api.types';
import { GetGroupFileRequest, GetFileGroupRequest } from '@/src/api/types/media.types';

function handleApiError(error: any): never {
  const status = error.response?.status || 500;
  const errorData = error.response?.data as ApiError;
  const errorMessage = errorData?.detail || errorData?.title || error.message;

  if (error.response) {
    throw {
      ...error,
      message: errorMessage,
    };
  }
  throw error;
}

export const mediaService = {
  getGroups: async <T = any>(params: GetGroupFileRequest, errorConfig?: ErrorConfig) => {
    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
    };

    try {
      const response = await apiClient.get<any, T>(
        `${API_PATHS.FILE_MANAGE.GET_GROUP_FILE}?skip=${params.skip}&limit=${params.limit}&partnerId=${params.partnerId}&search=${params.search}`,
        {
          partnerId: params.partnerId,
          search: params.search,
        },
        config
      );
      return response;
    } catch (error: any) {
      handleApiError(error);
    }
  },

  createGroup: async <T = any>(data: any, errorConfig?: ErrorConfig) => {
    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
    };

    try {
      const response = await apiClient.put<any, T>(
        API_PATHS.FILE_MANAGE.CREATE_GROUP_FILE,
        data,
        config
      );
      return response;
    } catch (error: any) {
      handleApiError(error);
    }
  },

  updateGroup: async <T = any>(data: any, errorConfig?: ErrorConfig) => {
    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
    };

    try {
      const response = await apiClient.post<any, T>(
        API_PATHS.FILE_MANAGE.UPDATE_GROUP_FILE,
        data,
        config
      );
      return response;
    } catch (error: any) {
      handleApiError(error);
    }
  },

  deleteGroup: async <T = any>(groupFileId: string, errorConfig?: ErrorConfig) => {
    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
    };

    try {
      const response = await apiClient.delete<T>(
        `${API_PATHS.FILE_MANAGE.DELETE_GROUP_FILE}?groupFileId=${groupFileId}`,
        config
      );
      return response;
    } catch (error: any) {
      handleApiError(error);
    }
  },

  getFiles: async <T = any>(params: GetFileGroupRequest, errorConfig?: ErrorConfig) => {
    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
    };

    try {
      const response = await apiClient.get<any, T>(
        `${API_PATHS.FILE_MANAGE.GET_FILE_GROUP}?skip=${params.skip}&limit=${params.limit}&groupFileId=${params.groupFileId}&search=${params.search}`,
        config
      );
      return response;
    } catch (error: any) {
      handleApiError(error);
    }
  },

  uploadFile: async <T = any>(data: any, errorConfig?: ErrorConfig) => {
    const formData = new FormData();
    formData.append('GroupId', data.groupId);
    formData.append('FileUpload', data.file);

    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    };

    try {
      const response = await apiClient.put<FormData, T>(
        `${API_PATHS.FILE_MANAGE.CREATE_FILE_GROUP}?GroupFileId=${data.groupId}`,
        formData,
        config
      );
      return response;
    } catch (error: any) {
      handleApiError(error);
    }
  },

  deleteFile: async <T = any>(mediaFileId: string, errorConfig?: ErrorConfig) => {
    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
    };

    try {
      const response = await apiClient.delete<T>(
        `${API_PATHS.FILE_MANAGE.DELETE_FILE_GROUP}?mediaFileId=${mediaFileId}`,
        config
      );
      return response;
    } catch (error: any) {
      handleApiError(error);
    }
  },
};

import { useState } from "react";
import { useStoreId } from "@/src/hooks/use-store-id";
import { GetProductRequest } from "@/src/api/types/product.types";
import {
  ExportProductParams,
  productService,
} from "@/src/api/services/dashboard/product/product.service";
import { ErrorHandlerService } from "@/src/api/services/error-handler.service";
import { StorageService } from "nextjs-api-lib";

export const useProduct = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const storeId = useStoreId();
  const partnerId = StorageService.get("partnerId") as string | null;

  const getProduct = async (
    skip: number,
    limit: number,
    itemsType: "Product" | "Service",
    categoryId?: string,
    subCategoryId?: string,
    search: string = ""
  ) => {
    try {
      setLoading(true);
      setError(null);

      const params: GetProductRequest = {
        shopId: storeId,
        partnerId,
        itemsType,
        categoryId: categoryId || null,
        subCategoryId: subCategoryId || null,
        search,
        skip,
        limit,
      };

      const response = await productService.getProduct(params);
      return response;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: true,
        logError: true,
      });
      setError(errorResponse.detail);
      throw errorResponse;
    } finally {
      setLoading(false);
    }
  };

  const createProduct = async (data: any) => {
    try {
      setLoading(true);
      setError(null);
      const response = await productService.createProduct({
        ...data,
        partnerId,
      });
      return response;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: true,
        logError: true,
      });
      setError(errorResponse.detail);
      throw errorResponse;
    } finally {
      setLoading(false);
    }
  };

  const updateProduct = async (data: any) => {
    try {
      setLoading(true);
      setError(null);
      const response = await productService.updateProduct({
        ...data,
        partnerId,
      });
      return response;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: true,
        logError: true,
      });
      setError(errorResponse.detail);
      throw errorResponse;
    } finally {
      setLoading(false);
    }
  };

  const updateProductImage = async (productId: string, imageFile: File) => {
    try {
      setLoading(true);
      setError(null);
      const response = await productService.updateImageProduct(productId, imageFile);
      return response;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: true,
        logError: true,
      });
      setError(errorResponse.detail);
      throw errorResponse;
    } finally {
      setLoading(false);
    }
  };

  const deleteProduct = async (itemsCode: string) => {
    try {
      setLoading(true);
      setError(null);
      const response = await productService.deleteProduct(itemsCode);
      return response;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: true,
        logError: true,
      });
      setError(errorResponse.detail);
      throw errorResponse;
    } finally {
      setLoading(false);
    }
  };

  const getProductDetail = async (productId: string) => {
    try {
      setLoading(true);
      setError(null);
      const response = await productService.getProductDetail(productId);
      return response;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: true,
        logError: true,
      });
      setError(errorResponse.detail);
      throw errorResponse;
    } finally {
      setLoading(false);
    }
  };

  const listItemsByItemsCode = async (data: any) => {
    try {
      setLoading(true);
      setError(null);
      const response = await productService.listItemsByItemsCode(data);
      return response;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: true,
        logError: true,
      });
      setError(errorResponse.detail);
    } finally {
      setLoading(false);
    }
  };

  const exportProductTemplate = async (data: any) => {
    try {
      setLoading(true);
      setError(null);
      const response = await productService.exportProductTemplate(data);
      return response;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: true,
        logError: true,
      });
      setError(errorResponse.detail);
      throw errorResponse;
    } finally {
      setLoading(false);
    }
  };
  const importProduct = async (data: any) => {
    try {
      setLoading(true);
      setError(null);
      const response = await productService.importProduct(data.file, data.shopId, data.type);
      return response;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: false,
        logError: true,
      });
      setError(errorResponse.detail);
      throw errorResponse;
    } finally {
      setLoading(false);
    }
  };
  const exportListProduct = async (data: ExportProductParams) => {
    try {
      setLoading(true);
      setError(null);
      const response = await productService.exportListProduct(data);
      return response;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: true,
        logError: true,
      });
      setError(errorResponse.detail);
      throw errorResponse;
    } finally {
      setLoading(false);
    }
  };

  return {
    getProduct,
    createProduct,
    updateProduct,
    updateProductImage,
    deleteProduct,
    getProductDetail,
    listItemsByItemsCode,
    exportProductTemplate,
    importProduct,
    exportListProduct,
    loading,
    error,
  };
};

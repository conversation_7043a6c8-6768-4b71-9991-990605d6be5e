import { apiClient } from "../../config/api-client";
import { API_PATHS } from "../../constants/api-paths";
import type { LoginRequest, ResetPasswordRequest, VerifyOtpRequest } from "../../types/auth.types";
import type { ApiError, ErrorConfig } from "../../types/error.types";
import type { ExtendedRequestConfig } from "../../types/api.types";
import md5 from "md5";

type LoginRequestData = Partial<Omit<LoginRequest, "password">> & {
  password: string;
};

function handleApiError(error: any): never {
  const status = error.response?.status || 500;
  const errorData = error.response?.data as ApiError;
  const errorMessage = errorData?.detail || errorData?.title || error.message;

  if (error.response) {
    throw {
      ...error,
      message: errorMessage,
    };
  }

  throw error;
}

export const authService = {
  login: async <T = any>(data: LoginRequestData, errorConfig?: ErrorConfig) => {
    const loginData: LoginRequest = {
      provider: data.provider || "Phone",
      phoneNumber: data.phoneNumber || "",
      email: data.email || "",
      username: data.username || "",
      password: data.password ? md5(data.password) : "",
      code2FA: data.code2FA || "",
    };

    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
      debug: false,
    };

    try {
      const response = await apiClient.post<LoginRequest, T>(
        API_PATHS.AUTH.LOGIN,
        loginData,
        config
      );

      return response;
    } catch (error: any) {
      handleApiError(error);
    }
  },

  logout: async <T = any>(refreshToken: string, tokenFCM: string, errorConfig?: ErrorConfig) => {
    const formData = new FormData();
    formData.append("refreshToken", refreshToken);
    formData.append("tokenFCM", tokenFCM);

    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
      headers: {
        "Content-Type": "multipart/form-data",
      },
    };

    try {
      const response = await apiClient.post<FormData, T>(API_PATHS.AUTH.LOGOUT, formData, config);

      return response;
    } catch (error: any) {
      handleApiError(error);
    }
  },

  checkPhoneNumber: async <T = any>(phoneNumber: string, errorConfig?: ErrorConfig) => {
    const formData = new FormData();
    formData.append("phoneNumber", phoneNumber);

    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
      headers: {
        "Content-Type": "multipart/form-data",
      },
    };

    try {
      const response = await apiClient.post<FormData, T>(
        API_PATHS.PROFILE.CHECK_PHONE_NUMBER,
        formData,
        config
      );

      return response;
    } catch (error: any) {
      handleApiError(error);
    }
  },

  sendOtp: async <T = any>(phoneNumber: string, errorConfig?: ErrorConfig) => {
    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
    };

    try {
      const response = await apiClient.post<{ phoneNumber: string }, T>(
        API_PATHS.OTP.SEND_OTP,
        { phoneNumber },
        config
      );

      return response;
    } catch (error: any) {
      handleApiError(error);
    }
  },

  verifyOtp: async <T = any>(data: VerifyOtpRequest, errorConfig?: ErrorConfig) => {
    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
    };

    try {
      const response = await apiClient.post<VerifyOtpRequest, T>(
        API_PATHS.OTP.VERIFY_OTP,
        data,
        config
      );

      return response;
    } catch (error: any) {
      handleApiError(error);
    }
  },

  resetPassword: async <T = any>(data: ResetPasswordRequest, errorConfig?: ErrorConfig) => {
    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
    };

    try {
      const response = await apiClient.post<ResetPasswordRequest, T>(
        API_PATHS.PROFILE.RESET_PASSWORD,
        data,
        config
      );

      return response;
    } catch (error: any) {
      handleApiError(error);
    }
  },

  getProfile: async <T = any>(errorConfig?: ErrorConfig) => {
    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
    };

    try {
      const response = await apiClient.get<T>(API_PATHS.PROFILE.GET_PROFILE, config);
      return response;
    } catch (error: any) {
      console.log("error get profile", error);
      handleApiError(error);
    }
  },

  updateProfile: async <T = any>(data: any, errorConfig?: ErrorConfig) => {
    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
    };

    try {
      const response = await apiClient.post<any, T>(API_PATHS.PROFILE.UPDATE_PROFILE, data, config);
      return response;
    } catch (error: any) {
      handleApiError(error);
    }
  },

  refreshToken: async <T = any>(refreshToken: string, errorConfig?: ErrorConfig) => {
    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
      headers: {
        "Content-Type": "application/json",
      },
    };

    try {
      const response = await apiClient.post<{ refreshToken: string }, T>(
        API_PATHS.AUTH.REFRESH_TOKEN,
        { refreshToken },
        config
      );

      return response;
    } catch (error: any) {
      handleApiError(error);
    }
  },

  registerDevice: async <T = any>(token: string, errorConfig?: ErrorConfig) => {
    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
    };

    try {
      const response = await apiClient.post<{ token: string }, T>(
        API_PATHS.AUTH.REGISTER_DEVICE,
        { token },
        config
      );

      return response;
    } catch (error: any) {
      handleApiError(error);
    }
  },

  revokeDevice: async <T = any>(token: string, errorConfig?: ErrorConfig) => {
    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
    };

    try {
      const response = await apiClient.post<{ token: string }, T>(
        API_PATHS.AUTH.REVOKE_DEVICE,
        { token },
        config
      );

      return response;
    } catch (error: any) {
      handleApiError(error);
    }
  },
};
